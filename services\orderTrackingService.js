const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

class OrderTrackingService {
    constructor() {
        this.dataDir = path.join(__dirname, '..', 'data');
        this.ordersFile = path.join(this.dataDir, 'orders.json');
        this.statusUpdatesFile = path.join(this.dataDir, 'status_updates.json');
        this.shipmentsFile = path.join(this.dataDir, 'shipments.json');
        this.errorsFile = path.join(this.dataDir, 'errors.json');

        this.initStorage();
    }

    async initStorage() {
        try {
            // Create data directory if it doesn't exist
            if (!fs.existsSync(this.dataDir)) {
                await mkdir(this.dataDir, { recursive: true });
            }

            // Initialize JSON files if they don't exist
            await this.ensureFile(this.ordersFile, []);
            await this.ensureFile(this.statusUpdatesFile, []);
            await this.ensureFile(this.shipmentsFile, []);
            await this.ensureFile(this.errorsFile, []);

            console.log('Order tracking storage initialized');
        } catch (error) {
            console.error('Error initializing storage:', error);
        }
    }

    async ensureFile(filePath, defaultValue) {
        if (!fs.existsSync(filePath)) {
            await writeFile(filePath, JSON.stringify(defaultValue, null, 2));
        }
    }

    async readJsonFile(filePath) {
        try {
            const data = await readFile(filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error(`Error reading ${filePath}:`, error);
            return [];
        }
    }

    async writeJsonFile(filePath, data) {
        try {
            await writeFile(filePath, JSON.stringify(data, null, 2));
        } catch (error) {
            console.error(`Error writing ${filePath}:`, error);
            throw error;
        }
    }

    // Create a new order
    async createOrder(orderKey1, bookId, orderData) {
        try {
            const orders = await this.readJsonFile(this.ordersFile);

            // Check if order already exists
            const existingIndex = orders.findIndex(o => o.orderKey1 === orderKey1);
            const order = {
                orderKey1,
                bookId,
                status: 'pending',
                orderData,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            if (existingIndex >= 0) {
                // Update existing order
                orders[existingIndex] = { ...orders[existingIndex], ...order };
            } else {
                // Add new order
                orders.push(order);
            }

            await this.writeJsonFile(this.ordersFile, orders);
            return { orderKey1, bookId };
        } catch (error) {
            console.error('Error creating order:', error);
            throw error;
        }
    }

    // Get order by orderKey1
    async getOrder(orderKey1) {
        try {
            const orders = await this.readJsonFile(this.ordersFile);
            return orders.find(o => o.orderKey1 === orderKey1) || null;
        } catch (error) {
            console.error('Error getting order:', error);
            return null;
        }
    }

    // Get all orders
    async getAllOrders() {
        try {
            const orders = await this.readJsonFile(this.ordersFile);
            return orders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        } catch (error) {
            console.error('Error getting all orders:', error);
            return [];
        }
    }

    // Update order status
    async updateOrderStatus(orderKey1, status) {
        try {
            const orders = await this.readJsonFile(this.ordersFile);
            const orderIndex = orders.findIndex(o => o.orderKey1 === orderKey1);

            if (orderIndex >= 0) {
                orders[orderIndex].status = status;
                orders[orderIndex].updatedAt = new Date().toISOString();
                await this.writeJsonFile(this.ordersFile, orders);
                return { changes: 1 };
            }

            return { changes: 0 };
        } catch (error) {
            console.error('Error updating order status:', error);
            throw error;
        }
    }

    // Add status update
    async addStatusUpdate(orderKey1, updateType, statusData) {
        try {
            const updates = await this.readJsonFile(this.statusUpdatesFile);
            const update = {
                id: Date.now().toString(),
                orderKey1,
                updateType,
                statusData,
                createdAt: new Date().toISOString()
            };

            updates.push(update);
            await this.writeJsonFile(this.statusUpdatesFile, updates);
            return { id: update.id };
        } catch (error) {
            console.error('Error adding status update:', error);
            throw error;
        }
    }

    // Add shipment
    async addShipment(orderKey1, shipmentData) {
        try {
            const shipments = await this.readJsonFile(this.shipmentsFile);
            const shipment = {
                id: Date.now().toString(),
                orderKey1,
                shipMethod: shipmentData.shipMethod,
                carrier: shipmentData.carrier,
                trackingNumber: shipmentData.trackingNumber,
                dateShipped: shipmentData.dateShipped,
                cost: shipmentData.cost,
                createdAt: new Date().toISOString()
            };

            shipments.push(shipment);
            await this.writeJsonFile(this.shipmentsFile, shipments);
            return { id: shipment.id };
        } catch (error) {
            console.error('Error adding shipment:', error);
            throw error;
        }
    }

    // Add error
    async addError(orderKey1, errorData) {
        try {
            const errors = await this.readJsonFile(this.errorsFile);
            const error = {
                id: Date.now().toString(),
                orderKey1,
                itemKey: errorData.itemKey,
                message: errorData.message,
                createdAt: new Date().toISOString()
            };

            errors.push(error);
            await this.writeJsonFile(this.errorsFile, errors);
            return { id: error.id };
        } catch (error) {
            console.error('Error adding error:', error);
            throw error;
        }
    }

    // Get order with all related data
    async getOrderWithDetails(orderKey1) {
        try {
            const order = await this.getOrder(orderKey1);
            if (!order) {
                return null;
            }

            // Get related data
            const [statusUpdates, shipments, errors] = await Promise.all([
                this.getStatusUpdatesForOrder(orderKey1),
                this.getShipmentsForOrder(orderKey1),
                this.getErrorsForOrder(orderKey1)
            ]);

            return {
                ...order,
                statusUpdates,
                shipments,
                errors
            };
        } catch (error) {
            console.error('Error getting order details:', error);
            return null;
        }
    }

    // Helper methods for getting related data
    async getStatusUpdatesForOrder(orderKey1) {
        try {
            const updates = await this.readJsonFile(this.statusUpdatesFile);
            return updates
                .filter(u => u.orderKey1 === orderKey1)
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        } catch (error) {
            console.error('Error getting status updates:', error);
            return [];
        }
    }

    async getShipmentsForOrder(orderKey1) {
        try {
            const shipments = await this.readJsonFile(this.shipmentsFile);
            return shipments
                .filter(s => s.orderKey1 === orderKey1)
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        } catch (error) {
            console.error('Error getting shipments:', error);
            return [];
        }
    }

    async getErrorsForOrder(orderKey1) {
        try {
            const errors = await this.readJsonFile(this.errorsFile);
            return errors
                .filter(e => e.orderKey1 === orderKey1)
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        } catch (error) {
            console.error('Error getting errors:', error);
            return [];
        }
    }

    // Get database statistics
    async getStats() {
        try {
            const [orders, updates, shipments, errors] = await Promise.all([
                this.readJsonFile(this.ordersFile),
                this.readJsonFile(this.statusUpdatesFile),
                this.readJsonFile(this.shipmentsFile),
                this.readJsonFile(this.errorsFile)
            ]);

            return {
                totalOrders: orders.length,
                totalUpdates: updates.length,
                totalShipments: shipments.length,
                totalErrors: errors.length,
                statusBreakdown: orders.reduce((acc, order) => {
                    acc[order.status] = (acc[order.status] || 0) + 1;
                    return acc;
                }, {})
            };
        } catch (error) {
            console.error('Error getting stats:', error);
            return {
                totalOrders: 0,
                totalUpdates: 0,
                totalShipments: 0,
                totalErrors: 0,
                statusBreakdown: {}
            };
        }
    }
}

module.exports = new OrderTrackingService(); 