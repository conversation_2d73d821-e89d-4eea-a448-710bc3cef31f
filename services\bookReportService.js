const fs = require('fs');
const path = require('path');
const { IMAGE_SERVICES } = require('../utils/config');

class BookReportService {
  constructor() {
    this.reportPath = path.join(__dirname, '..', 'output', 'book_report.csv');
    this.ensureReportFile();
  }

  ensureReportFile() {
    const outputDir = path.join(__dirname, '..', 'output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    if (!fs.existsSync(this.reportPath)) {
      const headers = 'Book ID,Book Title,Image Service,Generation Time (seconds),Number of Pages,Seed,Generated At\n';
      fs.writeFileSync(this.reportPath, headers);
    }
  }

  async addBookEntry(bookId, bookContent, imageService, generationTime, numberOfPages, seed) {
    const timestamp = new Date().toISOString();
    const entry = [
      bookId,
      `"${bookContent.title}"`,
      imageService,
      generationTime.toFixed(2),
      numberOfPages,
      seed,
      timestamp
    ].join(',') + '\n';

    try {
      await fs.promises.appendFile(this.reportPath, entry);
      console.log(`Added book entry to report: ${bookId}`);
    } catch (error) {
      console.error('Error adding book entry to report:', error);
      throw error;
    }
  }

  async getBookReport() {
    try {
      const report = await fs.promises.readFile(this.reportPath, 'utf-8');
      return report;
    } catch (error) {
      console.error('Error reading book report:', error);
      throw error;
    }
  }
}

module.exports = new BookReportService(); 