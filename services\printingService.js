const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const writeFile = promisify(fs.writeFile);
const unlink = promisify(fs.unlink);
const orderTrackingService = require('./orderTrackingService');

class PrintingService {
    constructor() {
        // Support both old Divvy API and new Alexanders API
        this.divvyBaseUrl = process.env.DIVVY_API_BASE_URL || 'https://api.divvypress.com';
        this.divvyApiKey = process.env.DIVVY_API_KEY;

        // Alexanders API configuration
        this.alexandersBaseUrl = process.env.ALEXANDERS_API_URL || 'https://devapi.divvy.systems';
        this.alexandersApiKey = process.env.ALEXANDERS_API_KEY;

        // Default shipping info for MEDL
        this.defaultShipping = {
            shipMethod: "UPSGROUND",
            address: {
                name: "MEDL Mobile Enterprises, LLC",
                address1: "8891 Research Dr.",
                city: "Irvine",
                state: "CA",
                zip: "92618",
                countryCode: "US"
            }
        };
    }

    // Legacy method for standard print items (Divvy API)
    async submitPrintJob(orderData) {
        try {
            const formData = new FormData();

            // Add required fields from the order data
            formData.append('orderKey1', orderData.orderKey1);
            formData.append('standardPrintItems[0][sku]', orderData.sku);
            formData.append('standardPrintItems[0][quantity]', orderData.quantity);
            formData.append('standardPrintItems[0][duplex]', orderData.duplex);

            // Add the file if it exists
            if (orderData.file && orderData.file.buffer) {
                formData.append('standardPrintItems[0][file]', orderData.file.buffer, {
                    filename: orderData.file.originalname,
                    contentType: orderData.file.mimetype
                });
            }

            const response = await axios.post(`${this.divvyBaseUrl}/v1.1/order`, formData, {
                headers: {
                    'X-API-KEY': this.divvyApiKey,
                    ...formData.getHeaders()
                }
            });

            return response.data;
        } catch (error) {
            console.error('Error submitting print job:', error);
            throw error;
        }
    }

    // Submit a photobook order to Alexanders
    async submitPhotobookOrder(orderData) {
        try {
            // Create the order payload
            const order = {
                orderKey1: orderData.orderKey1,
                photobookItems: [
                    {
                        coverUrl: orderData.coverUrl,
                        gutsUrl: orderData.gutsUrl,
                        sku: orderData.sku || "6x9-hardcover-book",
                        quantity: parseInt(orderData.quantity) || 1
                    }
                ],
                shipping: orderData.shipping || this.defaultShipping
            };

            const response = await axios.post(`${this.alexandersBaseUrl}/v1.1/order`, order, {
                headers: {
                    'X-API-KEY': this.alexandersApiKey,
                    'Content-Type': 'application/json'
                }
            });

            // Track the order in our database
            await orderTrackingService.createOrder(
                orderData.orderKey1,
                orderData.bookId,
                orderData
            );

            return response.data;
        } catch (error) {
            console.error('Error submitting photobook order:', error);
            throw error;
        }
    }

    // Get order status
    async getOrderStatus(orderKey) {
        try {
            // First check our local database
            const localOrder = await orderTrackingService.getOrderWithDetails(orderKey);

            // Then check with Alexanders API
            const response = await axios.get(`${this.alexandersBaseUrl}/v1.1/order/${orderKey}`, {
                headers: {
                    'X-API-KEY': this.alexandersApiKey
                }
            });

            // Combine local and remote data
            return {
                localOrder,
                remoteOrder: response.data
            };
        } catch (error) {
            console.error('Error getting order status:', error);
            // Return local data even if remote fails
            const localOrder = await orderTrackingService.getOrderWithDetails(orderKey);
            return {
                localOrder,
                remoteOrder: null,
                error: error.message
            };
        }
    }

    // Cancel order
    async cancelOrder(orderKey, apiType = 'alexanders') {
        try {
            let response;
            if (apiType === 'alexanders') {
                response = await axios.delete(`${this.alexandersBaseUrl}/v1.1/order/${orderKey}`, {
                    headers: {
                        'X-API-KEY': this.alexandersApiKey
                    }
                });
            } else {
                // Legacy Divvy API
                response = await axios.delete(`${this.divvyBaseUrl}/v1.1/order/${orderKey}`, {
                    headers: {
                        'X-API-KEY': this.divvyApiKey
                    }
                });
            }

            // Update local status
            await orderTrackingService.updateOrderStatus(orderKey, 'cancelled');

            return response.data;
        } catch (error) {
            console.error('Error canceling order:', error);
            throw error;
        }
    }

    // Handle webhook status updates from Alexanders
    async handleStatusUpdate(statusData) {
        try {
            console.log('Received status update:', statusData);

            const { orderKey1, updateType, data } = statusData;

            if (!orderKey1) {
                throw new Error('Missing orderKey1 in status update');
            }

            // Add the status update to our database
            await orderTrackingService.addStatusUpdate(orderKey1, updateType, data);

            // Update order status based on update type
            let newStatus = 'processing';
            if (updateType === 'printing') {
                newStatus = 'printing';
                if (data.dueDate) {
                    newStatus = 'scheduled';
                }
            } else if (updateType === 'shipped') {
                newStatus = 'shipped';
                // Add shipment data
                await orderTrackingService.addShipment(orderKey1, data);
            } else if (updateType === 'error') {
                newStatus = 'error';
                // Add error data
                await orderTrackingService.addError(orderKey1, data);
            }

            await orderTrackingService.updateOrderStatus(orderKey1, newStatus);

            return { success: true, message: 'Status update processed' };
        } catch (error) {
            console.error('Error handling status update:', error);
            throw error;
        }
    }

    // Handle specific webhook endpoints as per Alexanders API spec
    async handlePrintingStatusUpdate(orderKey1, statusData) {
        return this.handleStatusUpdate({
            orderKey1,
            updateType: 'printing',
            data: statusData
        });
    }

    async handleShipmentUpdate(orderKey1, shipmentData) {
        return this.handleStatusUpdate({
            orderKey1,
            updateType: 'shipped',
            data: shipmentData
        });
    }

    async handleErrorUpdate(orderKey1, errorData) {
        return this.handleStatusUpdate({
            orderKey1,
            updateType: 'error',
            data: errorData
        });
    }
}

module.exports = new PrintingService(); 