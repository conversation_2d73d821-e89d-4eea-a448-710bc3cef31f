// app.js
require('dotenv').config();
const express = require('express');
const bodyParser = require('body-parser');
const multer = require('multer');
const { OpenAI } = require('openai');
const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');
const Replicate = require('replicate');
const sharp = require('sharp');
const PNG = require('pngjs').PNG;
const { IMAGE_SERVICES, SERVICE_CONFIG } = require('./utils/config');
const { createPDF } = require('./services/pdfService');
const bookReportService = require('./services/bookReportService');
const printingService = require('./services/printingService');
const orderTrackingService = require('./services/orderTrackingService');

// Developer mode configuration
const DEV_MODE = false; // Set to true to enable developer mode
const DEV_DEFAULTS = {
  childName: 'Joseph',
  childAge: '4',
  childGender: 'boy',
  childInterests: 'dinosaurs',
  childHairColor: 'brown',
  childHairStyle: 'short',
  childEyeColor: 'brown',
  childSkinTone: 'fair',
  maxPages: 8 // Reduced from 12 to save memory
};

const app = express();
const port = process.env.PORT || 3000;

// Set up multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit
  }
});

// Set up middleware
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());
app.use(express.static('public'));

// Initialize APIs
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// Temporary file storage for Heroku (ephemeral filesystem)
const tempFileStorage = new Map();

// Store PDF in memory temporarily
function storePDFInMemory(bookId, pdfBuffer) {
  // Clean up old entries to free memory
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;

  for (const [key, value] of tempFileStorage.entries()) {
    if (now - value.timestamp > oneHour) {
      tempFileStorage.delete(key);
    }
  }

  // Limit storage to 10 PDFs max to prevent memory issues
  if (tempFileStorage.size >= 10) {
    const oldestKey = tempFileStorage.keys().next().value;
    tempFileStorage.delete(oldestKey);
  }

  tempFileStorage.set(bookId, {
    buffer: pdfBuffer,
    timestamp: Date.now(),
    filename: `${bookId}.pdf`,
    size: pdfBuffer.length
  });

  // Clean up after 30 minutes (reduced from 1 hour)
  setTimeout(() => {
    tempFileStorage.delete(bookId);
  }, 30 * 60 * 1000);
}

// Get PDF from memory
function getPDFFromMemory(bookId) {
  return tempFileStorage.get(bookId);
}

// Get all available PDFs from memory
function getAllPDFsFromMemory() {
  const pdfs = [];
  for (const [bookId, data] of tempFileStorage.entries()) {
    pdfs.push({
      id: bookId,
      filename: data.filename,
      timestamp: data.timestamp,
      size: data.size,
      downloadUrl: `/download/${bookId}`
    });
  }
  return pdfs;
}

app.get('/', (req, res) => {
  if (DEV_MODE) {
    res.sendFile(path.join(__dirname, 'public', 'develop.html'));
  } else {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
  }
});

app.get('/printing', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'printing.html'));
});

app.get('/test-async', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'test-async.html'));
});

app.get('/pdf-monitor', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'pdf-monitor.html'));
});

app.get('/print-monitor', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'print-monitor.html'));
});

app.post('/generate-book', async (req, res) => {
  const startTime = performance.now();

  // Use developer defaults if in dev mode, otherwise use request body
  const {
    childName = DEV_MODE ? DEV_DEFAULTS.childName : undefined,
    childAge = DEV_MODE ? DEV_DEFAULTS.childAge : undefined,
    childGender = DEV_MODE ? DEV_DEFAULTS.childGender : undefined,
    childInterests = DEV_MODE ? DEV_DEFAULTS.childInterests : undefined,
    childHairColor = DEV_MODE ? DEV_DEFAULTS.childHairColor : undefined,
    childHairStyle = DEV_MODE ? DEV_DEFAULTS.childHairStyle : undefined,
    childEyeColor = DEV_MODE ? DEV_DEFAULTS.childEyeColor : undefined,
    childSkinTone = DEV_MODE ? DEV_DEFAULTS.childSkinTone : undefined,
    imageService = IMAGE_SERVICES.DALL_E
  } = req.body;

  if (!DEV_MODE && (!childName || !childAge || !childGender || !childInterests || !childHairColor || !childHairStyle || !childEyeColor || !childSkinTone)) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    console.log('Starting book generation process...');
    if (DEV_MODE) {
      console.log('Running in developer mode with defaults');
    }
    console.log(`Using image service: ${imageService}`);

    // Set a unique book ID for this generation
    const bookId = Date.now().toString();
    const outputDir = path.join(__dirname, 'output');
    const bookDir = path.join(outputDir, bookId);
    const outputPath = path.join(bookDir, `${bookId}.pdf`);

    // Generate a consistent seed for all images in this book
    const bookSeed = Math.floor(Math.random() * 1000000);
    console.log(`Using seed ${bookSeed} for all images in book ${bookId}`);

    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Create book-specific directory
    if (!fs.existsSync(bookDir)) {
      fs.mkdirSync(bookDir, { recursive: true });
    }

    // For Heroku deployment, we'll implement a simple polling mechanism
    // First, return immediately with a job ID
    const jobId = `job-${bookId}`;

    // Store job status in memory (in production, use Redis or database)
    if (!global.jobStatus) global.jobStatus = {};
    global.jobStatus[jobId] = {
      status: 'processing',
      progress: 0,
      bookId: bookId,
      message: 'Starting book generation...'
    };

    // Start the generation process in the background
    generateBookInBackground(
      childName,
      childAge,
      childGender,
      childInterests,
      childHairColor,
      childHairStyle,
      childEyeColor,
      childSkinTone,
      DEV_MODE ? DEV_DEFAULTS.maxPages : undefined,
      imageService,
      bookId,
      bookSeed,
      bookDir,
      outputPath,
      jobId,
      startTime
    );

    // Return immediately with job ID
    res.json({
      jobId: jobId,
      message: 'Book generation started. Use the job ID to check progress.',
      statusUrl: `/api/book-status/${jobId}`
    });

  } catch (error) {
    console.error('Error starting book generation:', error);
    res.status(500).json({ error: 'Failed to start book generation' });
  }
});

// New endpoint to check book generation status
app.get('/api/book-status/:jobId', (req, res) => {
  const { jobId } = req.params;

  if (!global.jobStatus || !global.jobStatus[jobId]) {
    return res.status(404).json({ error: 'Job not found' });
  }

  const job = global.jobStatus[jobId];
  res.json(job);
});

// Background function to generate book
async function generateBookInBackground(
  childName,
  childAge,
  childGender,
  childInterests,
  childHairColor,
  childHairStyle,
  childEyeColor,
  childSkinTone,
  maxPages,
  imageService,
  bookId,
  bookSeed,
  bookDir,
  outputPath,
  jobId,
  startTime
) {
  try {
    // Ensure startTime is defined
    const actualStartTime = startTime || performance.now();

    // Update job status
    global.jobStatus[jobId].message = 'Generating book content...';
    global.jobStatus[jobId].progress = 10;

    // Step 1: Generate book content with OpenAI
    console.log('Generating book content...');
    const bookContent = await generateBookContent(
      childName,
      childAge,
      childGender,
      childInterests,
      childHairColor,
      childHairStyle,
      childEyeColor,
      childSkinTone,
      maxPages
    );

    // Ensure childName is set for PDF
    bookContent.childName = childName;

    // Save book content as JSON
    const bookContentPath = path.join(bookDir, `${bookId}_content.json`);
    fs.writeFileSync(bookContentPath, JSON.stringify(bookContent, null, 2));

    global.jobStatus[jobId].message = 'Generating images...';
    global.jobStatus[jobId].progress = 20;

    // Step 2: Generate images with selected service
    console.log('Generating images...');
    const config = SERVICE_CONFIG[imageService];
    const images = [];
    const imagePaths = [];

    // Track if we need to fallback to a different service
    let useFallbackService = false;
    let fallbackService = IMAGE_SERVICES.DALL_E;

    // Generate title page image first
    console.log('Generating title page image...');
    global.jobStatus[jobId].message = 'Generating title page image...';
    global.jobStatus[jobId].progress = 25;

    const titlePagePrompt = `Create a stunning children's book cover illustration that captures the essence of a story titled "${bookContent.title}". The scene should reflect themes of ${childInterests}. The main character should be a ${childGender} with ${childHairColor} ${childHairStyle} hair, ${childEyeColor} eyes, and ${childSkinTone} skin. Make it whimsical and engaging, with rich colors and enchanting lighting. Style: professional children's book illustration with soft edges and magical atmosphere. The image must include the book title  - this is critically important.The title should be placed near the top of the page, in a playful, easy-to-read font. Make sure the title is sized so it fits well within the page boundaries, with generous margins on all sides, and does not touch or bleed outside the edges. The illustration should leave space at the top for the title and focus the main artwork in the center and lower part of the page.`;

    let titlePageImage;
    const titlePageOptions = {
      negative_prompt: "",
      seed: bookSeed
    };

    try {
      switch (imageService) {
        case IMAGE_SERVICES.DALL_E:
          titlePageImage = await generateImageWithDalle(titlePagePrompt, 0, titlePageOptions);
          break;
        case IMAGE_SERVICES.MIDJOURNEY:
          titlePageImage = await generateImageWithMidjourney(titlePagePrompt, 0, titlePageOptions);
          break;
        case IMAGE_SERVICES.PRUNA:
          titlePageImage = await generateImageWithPruna(titlePagePrompt, 0, titlePageOptions);
          break;
        case IMAGE_SERVICES.RECRAFT:
          titlePageImage = await generateImageWithRecraft(titlePagePrompt, 0, titlePageOptions);
          break;
        case IMAGE_SERVICES.GPT_IMAGE_1:
          try {
            titlePageImage = await generateImageWithGPTImage1(titlePagePrompt, 0, titlePageOptions);
          } catch (error) {
            console.log('GPT Image 1 failed for title page, using DALL-E fallback...');
            titlePageImage = await generateImageWithDalle(titlePagePrompt, 0, titlePageOptions);
          }
          break;
        default:
          throw new Error(`Unsupported image service: ${imageService}`);
      }
    } catch (error) {
      if (error.message?.includes('E005') || error.message?.includes('flagged as sensitive')) {
        console.log('E005 error detected, switching to DALL-E as fallback...');
        useFallbackService = true;
        global.jobStatus[jobId].message = 'Switching to alternative image service...';
        titlePageImage = await generateImageWithDalle(titlePagePrompt, 0, titlePageOptions);
      } else {
        throw error;
      }
    }    // Save original title page image
    const titleImagePath = path.join(bookDir, `${bookId}_image_0_original.png`);
    const titleImageBuffer = await convertImageToBuffer(titlePageImage);
    fs.writeFileSync(titleImagePath, titleImageBuffer);

    // Convert to base64 for the images array (used by PDF service)
    const titleBase64Image = titleImageBuffer.toString('base64');
    images.push(titleBase64Image);
    imagePaths.push({
      original: titleImagePath,
      upscaled: null
    });

    // Store the flag in bookContent for the PDF service to use
    bookContent.titlePageIncludesText = true;

    global.jobStatus[jobId].progress = 30;

    // Process images based on service for story pages
    if (imageService === IMAGE_SERVICES.MIDJOURNEY) {
      // Start from i = 1 to skip title page
      for (let i = 1; i < bookContent.pages.length; i++) {
        console.log(`Generating image ${i}/${bookContent.pages.length - 1}...`);
        global.jobStatus[jobId].message = `Generating image ${i}/${bookContent.pages.length - 1}...`;
        global.jobStatus[jobId].progress = 30 + Math.floor((i / (bookContent.pages.length - 1)) * 50);

        const enhancedPrompt = `${bookContent.pages[i].imagePrompt} The main character is a ${childGender} with ${childHairColor} ${childHairStyle} hair, ${childEyeColor} eyes, and ${childSkinTone} skin. Create a pure illustration without any text elements. The illustration should be in a children's book style with soft edges and vibrant colors. Important: The image must be completely free of any text, letters, numbers, or written elements. Style: professional children's book illustration with soft edges and magical atmosphere, consistent with the title page.  There should be not text on the image - this is critically important.`; const image = await generateImageWithMidjourney(enhancedPrompt, i, {
          negative_prompt: "text, letters, numbers, words, writing, typography, fonts, signage, labels, logos, signatures, watermarks, title, credits, book title, character names, any writing or symbols of any kind, speech bubbles, thought bubbles, captions, subtitles, dialogue, quotes, any form of text or writing",
          seed: bookSeed
        });

        // Save original image
        const imagePath = path.join(bookDir, `${bookId}_image_${i}_original.png`);
        const imageBuffer = await convertImageToBuffer(image); fs.writeFileSync(imagePath, imageBuffer);

        // Convert to base64 for the images array (used by PDF service)
        const base64Image = imageBuffer.toString('base64');
        images.push(base64Image);
        imagePaths.push({
          original: imagePath,
          upscaled: null
        });

        if (i < bookContent.pages.length - 1) {
          console.log(`Waiting ${config.batchDelay / 1000} seconds before next batch...`);
          await new Promise(resolve => setTimeout(resolve, config.batchDelay));
        }
      }
    } else if (imageService === IMAGE_SERVICES.PRUNA) {
      // Start from i = 1 to skip title page
      for (let i = 1; i < bookContent.pages.length; i++) {
        console.log(`Generating image ${i}/${bookContent.pages.length - 1}...`);
        global.jobStatus[jobId].message = `Generating image ${i}/${bookContent.pages.length - 1}...`;
        global.jobStatus[jobId].progress = 30 + Math.floor((i / (bookContent.pages.length - 1)) * 50);

        const enhancedPrompt = `${bookContent.pages[i].imagePrompt} The main character is a ${childGender} with ${childHairColor} ${childHairStyle} hair, ${childEyeColor} eyes, and ${childSkinTone} skin. Create a pure illustration without any text elements. The illustration should be in a children's book style with soft edges and vibrant colors. Important: The image must be completely free of any text, letters, numbers, or written elements. Style: professional children's book illustration with soft edges and magical atmosphere, consistent with the title page.`; const image = await generateImageWithPruna(enhancedPrompt, i, {
          negative_prompt: "text, letters, numbers, words, writing, typography, fonts, signage, labels, logos, signatures, watermarks, title, credits, book title, character names, any writing or symbols of any kind, speech bubbles, thought bubbles, captions, subtitles, dialogue, quotes, any form of text or writing",
          seed: bookSeed
        });

        // Save original image
        const imagePath = path.join(bookDir, `${bookId}_image_${i}_original.png`);
        const imageBuffer = await convertImageToBuffer(image); fs.writeFileSync(imagePath, imageBuffer);

        // Convert to base64 for the images array (used by PDF service)
        const base64Image = imageBuffer.toString('base64');
        images.push(base64Image);
        imagePaths.push({
          original: imagePath,
          upscaled: null
        });

        if (i < bookContent.pages.length - 1) {
          console.log(`Waiting ${config.batchDelay / 1000} seconds before next batch...`);
          await new Promise(resolve => setTimeout(resolve, config.batchDelay));
        }
      }
    } else {
      // For other services, generate all images at once
      global.jobStatus[jobId].message = 'Generating story images...';
      global.jobStatus[jobId].progress = 40;

      // Start from i = 1 to skip title page
      for (let i = 1; i < bookContent.pages.length; i++) {
        console.log(`Generating image ${i}/${bookContent.pages.length - 1}...`);
        global.jobStatus[jobId].message = `Generating image ${i}/${bookContent.pages.length - 1}...`;
        global.jobStatus[jobId].progress = 40 + Math.floor((i / (bookContent.pages.length - 1)) * 40);

        const enhancedPrompt = `${bookContent.pages[i].imagePrompt} The main character is a ${childGender} with ${childHairColor} ${childHairStyle} hair, ${childEyeColor} eyes, and ${childSkinTone} skin. Create a pure illustration without any text elements. The illustration should be in a children's book style with soft edges and vibrant colors. Important: The image must be completely free of any text, letters, numbers, or written elements. Style: professional children's book illustration with soft edges and magical atmosphere, consistent with the title page.`;

        let image;
        const currentService = useFallbackService ? fallbackService : imageService;

        switch (currentService) {
          case IMAGE_SERVICES.DALL_E:
            image = await generateImageWithDalle(enhancedPrompt, i, {
              negative_prompt: "text, letters, numbers, words, writing, typography, fonts, signage, labels, logos, signatures, watermarks, title, credits, book title, character names, any writing or symbols of any kind, speech bubbles, thought bubbles, captions, subtitles, dialogue, quotes, any form of text or writing",
              seed: bookSeed
            });
            break;
          case IMAGE_SERVICES.RECRAFT:
            image = await generateImageWithRecraft(enhancedPrompt, i, {
              negative_prompt: "text, letters, numbers, words, writing, typography, fonts, signage, labels, logos, signatures, watermarks, title, credits, book title, character names, any writing or symbols of any kind, speech bubbles, thought bubbles, captions, subtitles, dialogue, quotes, any form of text or writing",
              seed: bookSeed
            });
            break;
          case IMAGE_SERVICES.GPT_IMAGE_1:
            try {
              image = await generateImageWithGPTImage1(enhancedPrompt, i, {
                negative_prompt: "text, letters, numbers, words, writing, typography, fonts, signage, labels, logos, signatures, watermarks, title, credits, book title, character names, any writing or symbols of any kind, speech bubbles, thought bubbles, captions, subtitles, dialogue, quotes, any form of text or writing",
                seed: bookSeed
              });
            } catch (error) {
              if (error.message?.includes('E005') || error.message?.includes('flagged as sensitive')) {
                console.log(`E005 error on page ${i}, using DALL-E fallback...`);
                image = await generateImageWithDalle(enhancedPrompt, i, {
                  negative_prompt: "text, letters, numbers, words, writing, typography, fonts, signage, labels, logos, signatures, watermarks, title, credits, book title, character names, any writing or symbols of any kind, speech bubbles, thought bubbles, captions, subtitles, dialogue, quotes, any form of text or writing",
                  seed: bookSeed
                });
              } else {
                throw error;
              }
            }
            break;
          default:
            throw new Error(`Unsupported image service: ${currentService}`);
        }        // Save original image
        const imagePath = path.join(bookDir, `${bookId}_image_${i}_original.png`); const imageBuffer = await convertImageToBuffer(image);
        fs.writeFileSync(imagePath, imageBuffer);

        // Convert to base64 for the images array (used by PDF service)
        const base64Image = imageBuffer.toString('base64');
        images.push(base64Image);
        imagePaths.push({
          original: imagePath,
          upscaled: null
        });

        if (i < bookContent.pages.length - 1) {
          console.log(`Waiting ${config.baseDelay / 1000} seconds before next request...`);
          await new Promise(resolve => setTimeout(resolve, config.baseDelay));
        }
      }
    }

    global.jobStatus[jobId].message = 'Creating PDF...';
    global.jobStatus[jobId].progress = 85;

    // Step 3: Create PDF
    console.log('Creating PDF...');
    const pdfBuffer = await createPDF(outputPath, bookContent, images, true);

    // Store PDF in memory temporarily
    storePDFInMemory(bookId, pdfBuffer);

    global.jobStatus[jobId].message = 'Book generation completed!';
    global.jobStatus[jobId].progress = 100;
    global.jobStatus[jobId].status = 'completed';
    global.jobStatus[jobId].downloadUrl = `/download/${bookId}`;

    const endTime = performance.now();
    const duration = Math.round((endTime - actualStartTime) / 1000);
    console.log(`Book generation completed in ${duration} seconds`);

  } catch (error) {
    console.error('Error in background book generation:', error);
    global.jobStatus[jobId].status = 'error';
    global.jobStatus[jobId].message = `Error: ${error.message}`;
  }
}

app.get('/download/:bookId', (req, res) => {
  const bookId = req.params.bookId;
  console.log(`Download request for book ID: ${bookId}`);

  // First try to get from memory storage (primary method for Heroku)
  const pdfData = getPDFFromMemory(bookId);
  if (pdfData) {
    console.log(`Serving PDF from memory for book ID: ${bookId}`);
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${pdfData.filename}"`);
    res.setHeader('Content-Length', pdfData.size);
    res.setHeader('Cache-Control', 'no-cache');
    res.send(pdfData.buffer);
    return;
  }

  // Fallback to filesystem (for local development only)
  const filePath = path.join(__dirname, 'output', bookId, `${bookId}.pdf`);
  if (fs.existsSync(filePath)) {
    console.log(`Serving PDF from filesystem for book ID: ${bookId}`);
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${bookId}.pdf"`);
    res.setHeader('Cache-Control', 'no-cache');
    res.sendFile(filePath);
  } else {
    console.log(`PDF not found for book ID: ${bookId}`);
    res.status(404).json({
      error: 'PDF not found',
      message: 'The PDF file may have been cleaned up or not yet generated. Please regenerate the book.',
      bookId: bookId,
      availableBooks: getAllPDFsFromMemory().map(pdf => pdf.id)
    });
  }
});

// Add endpoint to regenerate PDF from existing book data
app.get('/regenerate-pdf/:bookId', async (req, res) => {
  const bookId = req.params.bookId;
  const bookDir = path.join(__dirname, 'output', bookId);

  try {
    // Check if book directory exists
    if (!fs.existsSync(bookDir)) {
      return res.status(404).json({ error: 'Book not found' });
    }

    // Load book content JSON
    const contentPath = path.join(bookDir, `${bookId}_content.json`);
    if (!fs.existsSync(contentPath)) {
      return res.status(404).json({ error: 'Book content not found' });
    }

    const bookContent = JSON.parse(fs.readFileSync(contentPath, 'utf8'));

    // Find and load upscaled images
    const upscaledImages = [];
    for (let i = 0; i < bookContent.pages.length; i++) {
      // Check for the 8-bit version first
      const upscaledImage8bitPath = path.join(bookDir, `${bookId}_image_${i}_upscaled_8bit.png`);
      // Then check for the 16-bit version
      const upscaledImage16bitPath = path.join(bookDir, `${bookId}_image_${i}_upscaled_16bit.png`);
      // Then check for legacy upscaled version
      const upscaledImagePath = path.join(bookDir, `${bookId}_image_${i}_upscaled.png`);
      // Finally check for original image
      const originalImagePath = path.join(bookDir, `${bookId}_image_${i}_original.png`);

      let imagePath;
      // Try to find the best available image, preferring 8-bit
      if (fs.existsSync(upscaledImage8bitPath)) {
        imagePath = upscaledImage8bitPath;
      } else if (fs.existsSync(upscaledImage16bitPath)) {
        // If only 16-bit exists, convert it to 8-bit
        const newUpscaledImage8bitPath = path.join(bookDir, `${bookId}_image_${i}_upscaled_8bit.png`);
        await convertTo8BitPng(upscaledImage16bitPath, newUpscaledImage8bitPath);
        imagePath = newUpscaledImage8bitPath;
      } else if (fs.existsSync(upscaledImagePath)) {
        imagePath = upscaledImagePath;
      } else if (fs.existsSync(originalImagePath)) {
        imagePath = originalImagePath;
      } else {
        return res.status(404).json({ error: `Image for page ${i} not found` });
      }

      const imageBuffer = fs.readFileSync(imagePath);
      upscaledImages.push(imageBuffer.toString('base64'));
    }

    // Generate a new PDF
    const pdfPath = path.join(bookDir, `${bookId}_regenerated.pdf`);
    await createPDF(pdfPath, bookContent, upscaledImages);

    res.json({
      success: true,
      message: 'PDF regenerated successfully',
      bookPath: `/download/${bookId}/${bookId}_regenerated.pdf`
    });

  } catch (error) {
    console.error('Error regenerating PDF:', error);
    res.status(500).json({ error: 'Failed to regenerate PDF', details: error.message });
  }
});

// Helper function to convert image data to Buffer for saving
async function convertImageToBuffer(imageData) {
  console.log(`convertImageToBuffer called with type: ${typeof imageData}, constructor: ${imageData?.constructor?.name}`);

  if (!imageData) {
    throw new Error('No image data provided');
  }
  // If it's already a Buffer, return it
  if (Buffer.isBuffer(imageData)) {
    console.log('Image data is already a Buffer');
    return await ensurePDFCompatibleFormat(imageData);
  }  // If it's a base64 string with data URL prefix
  if (typeof imageData === 'string' && imageData.startsWith('data:')) {
    console.log('Image data is a data URL');
    const base64Data = imageData.split(',')[1]; // Remove data:image/png;base64, prefix
    const buffer = Buffer.from(base64Data, 'base64');
    return await ensurePDFCompatibleFormat(buffer);
  }

  // If it's a plain string
  if (typeof imageData === 'string') {
    console.log(`String analysis: length=${imageData.length}, first50="${imageData.substring(0, 50)}", starts with http=${imageData.startsWith('http')}`);
    // Check if it looks like a URL first
    if (imageData.startsWith('http://') || imageData.startsWith('https://')) {
      console.log('Image data is a URL string, fetching...');
      try {
        // Try to use built-in fetch first (Node.js 18+)
        let fetchFunction;
        if (typeof fetch !== 'undefined') {
          fetchFunction = fetch;
        } else {
          // Fallback to dynamic import for older Node.js versions
          const { default: nodeFetch } = await import('node-fetch');
          fetchFunction = nodeFetch;
        }
        const response = await fetchFunction(imageData);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.statusText}`);
        }
        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        return await ensurePDFCompatibleFormat(buffer);
      } catch (fetchError) {
        console.error('Failed to fetch image from URL:', fetchError.message);
        throw new Error(`Unable to fetch image from URL: ${imageData}`);
      }
    } else {      // If it doesn't look like a URL, try base64 conversion
      console.log('Image data is a string, attempting base64 conversion...');
      try {
        const buffer = Buffer.from(imageData, 'base64');
        return await ensurePDFCompatibleFormat(buffer);
      } catch (error) {
        console.error('Base64 conversion failed for string:', imageData.substring(0, 100) + '...');
        throw new Error(`Unable to process image data: not valid base64 or URL`);
      }
    }
  }

  // If it's a function (which might be a ReadableStream or FileOutput)
  if (typeof imageData === 'function') {
    console.log('Image data is a function - this might be a ReadableStream issue', imageData);
    throw new Error('Image data appears to be a function - check the API response format');
  }
  // If it's an object that might be a ReadableStream or FileOutput
  if (typeof imageData === 'object' && imageData !== null) {
    console.log(`Image data is an object with constructor: ${imageData.constructor?.name}`);

    // Check if it's a URL object
    if (imageData.constructor?.name === 'URL') {
      console.log('Image data is a URL object, fetching from href...');
      const urlString = imageData.href;
      try {
        let fetchFunction;
        if (typeof fetch !== 'undefined') {
          fetchFunction = fetch;
        } else {
          const { default: nodeFetch } = await import('node-fetch');
          fetchFunction = nodeFetch;
        }
        const response = await fetchFunction(urlString);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.statusText}`);
        }
        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        return await ensurePDFCompatibleFormat(buffer);
      } catch (fetchError) {
        console.error('Failed to fetch image from URL object:', fetchError.message);
        throw new Error(`Unable to fetch image from URL: ${urlString}`);
      }
    }

    // Check if it's a ReadableStream
    if (imageData.constructor?.name === 'ReadableStream' || (imageData.getReader && typeof imageData.getReader === 'function')) {
      console.log('Converting ReadableStream to Buffer...');
      const chunks = [];
      const reader = imageData.getReader();

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          chunks.push(value);
        }
      } finally {
        reader.releaseLock();
      }
      // Combine all chunks into a single buffer
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const resultBuffer = new Uint8Array(totalLength);
      let offset = 0;
      for (const chunk of chunks) {
        resultBuffer.set(chunk, offset);
        offset += chunk.length;
      }

      const buffer = Buffer.from(resultBuffer);
      return await ensurePDFCompatibleFormat(buffer);
    }

    // Check if it has a URL property
    if (imageData.url && typeof imageData.url === 'string') {
      console.log('Image data has URL property, fetching...');
      try {
        let fetchFunction;
        if (typeof fetch !== 'undefined') {
          fetchFunction = fetch;
        } else {
          const { default: nodeFetch } = await import('node-fetch');
          fetchFunction = nodeFetch;
        }
        const response = await fetchFunction(imageData.url);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.statusText}`);
        }
        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        return await ensurePDFCompatibleFormat(buffer);
      } catch (fetchError) {
        console.error('Failed to fetch image from URL:', fetchError.message);
        throw new Error(`Unable to fetch image from URL: ${imageData.url}`);
      }
    }
  }

  throw new Error(`Unsupported image data type: ${typeof imageData}, constructor: ${imageData?.constructor?.name}`);
}

// Generate book content using OpenAI
async function generateBookContent(
  childName,
  childAge,
  childGender,
  childInterests,
  childHairColor,
  childHairStyle,
  childEyeColor,
  childSkinTone,
  maxPages = undefined
) {
  const maxRetries = 3;
  let retryCount = 0;

  while (retryCount < maxRetries) {
    try {
      const prompt = `
        Create a children's story book for a ${childAge} year old ${childGender} named ${childName} who loves ${childInterests}.
        The child has ${childHairColor} ${childHairStyle} hair, ${childEyeColor} eyes, and ${childSkinTone} skin.
        The book should be exactly ${maxPages || 30} pages long (15 physical pages, since each physical page has two sides).
        For each page, provide:
        1. The page text (appropriate for the child's age, keep it concise)
        2. A detailed image prompt for an AI image generator that includes the child's appearance:
           - Gender: ${childGender}
           - Hair: ${childHairColor} ${childHairStyle}
           - Eyes: ${childEyeColor}
           - Skin: ${childSkinTone}
        
        Format the response as a JSON object with:
        {
          "title": "The book title",
          "author": "AI Author",
          "pages": [
            { 
              "pageType": "title", 
              "text": "Title page text", 
              "imagePrompt": "detailed image prompt"
            },
            {
              "pageType": "story",
              "text": "Page 1 text",
              "imagePrompt": "detailed image prompt"
            },
            // ... continue for all ${maxPages || 30} pages ...
            {
              "pageType": "ending",
              "text": "Ending page text",
              "imagePrompt": "detailed image prompt"
            }
          ]
        }
      `;

      const response = await openai.chat.completions.create({
        model: "gpt-4-turbo",
        messages: [
          {
            role: "system",
            content: "You are a children's book author specializing in personalized stories that feature the child as the main character. Create engaging, age-appropriate content with vivid descriptions. The story should have a clear beginning, middle, and end, with each page contributing to the narrative arc. Keep the text on each page concise but meaningful."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000,
        response_format: { type: "json_object" }
      });

      return JSON.parse(response.choices[0].message.content);

    } catch (error) {
      retryCount++;
      console.error(`OpenAI API error (attempt ${retryCount}/${maxRetries}):`, error.message);

      if (error.message.includes('520') || error.message.includes('Cloudflare') || error.message.includes('unknown error')) {
        if (retryCount < maxRetries) {
          const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff: 2s, 4s, 8s
          console.log(`OpenAI server error. Retrying in ${delay / 1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
      }

      if (retryCount >= maxRetries) {
        throw new Error(`OpenAI API failed after ${maxRetries} attempts: ${error.message}`);
      }
    }
  }
}

// Generate an image using OpenAI
async function generateImageWithDalle(prompt, index, options = {}) {
  const config = SERVICE_CONFIG[IMAGE_SERVICES.DALL_E];
  let retryCount = 0;

  while (retryCount < config.maxRetries) {
    try {
      if (index > 0) {
        await new Promise(resolve => setTimeout(resolve, config.baseDelay * (index % 3)));
      }

      const response = await openai.images.generate({
        model: "dall-e-3",
        prompt: prompt + (options.negative_prompt ? `\nNegative prompt: ${options.negative_prompt}` : ''),
        n: 1,
        size: "1024x1024",
        quality: "standard",
        style: "vivid"
      });

      return response.data[0].url;
    } catch (error) {
      retryCount++;
      console.error(`DALL-E API error (attempt ${retryCount}/${config.maxRetries}):`, error.message);

      if (retryCount >= config.maxRetries) {
        throw new Error(`DALL-E API failed after ${config.maxRetries} attempts: ${error.message}`);
      }

      await new Promise(resolve => setTimeout(resolve, config.baseDelay));
    }
  }
}

// Generate an image using Midjourney
async function generateImageWithMidjourney(prompt, index, options = {}) {
  const config = SERVICE_CONFIG[IMAGE_SERVICES.MIDJOURNEY];
  let retryCount = 0;

  while (retryCount < config.maxRetries) {
    try {
      if (index > 0) {
        await new Promise(resolve => setTimeout(resolve, config.baseDelay * (index % 3)));
      }

      const response = await replicate.run(config.model, {
        input: {
          prompt: prompt + (options.negative_prompt ? ` --no ${options.negative_prompt}` : ''),
          ...options
        }
      });

      return response[0];
    } catch (error) {
      retryCount++;
      console.error(`Midjourney API error (attempt ${retryCount}/${config.maxRetries}):`, error.message);

      if (retryCount >= config.maxRetries) {
        throw new Error(`Midjourney API failed after ${config.maxRetries} attempts: ${error.message}`);
      }

      await new Promise(resolve => setTimeout(resolve, config.baseDelay));
    }
  }
}

// Generate an image using Pruna
async function generateImageWithPruna(prompt, index, options = {}) {
  const config = SERVICE_CONFIG[IMAGE_SERVICES.PRUNA];
  let retryCount = 0;

  while (retryCount < config.maxRetries) {
    try {
      if (index > 0) {
        await new Promise(resolve => setTimeout(resolve, config.baseDelay * (index % 3)));
      }

      const response = await replicate.run(config.model, {
        input: {
          prompt: prompt,
          negative_prompt: options.negative_prompt || "",
          ...options
        }
      });

      return response[0];
    } catch (error) {
      retryCount++;
      console.error(`Pruna API error (attempt ${retryCount}/${config.maxRetries}):`, error.message);

      if (retryCount >= config.maxRetries) {
        throw new Error(`Pruna API failed after ${config.maxRetries} attempts: ${error.message}`);
      }

      await new Promise(resolve => setTimeout(resolve, config.baseDelay));
    }
  }
}

// Generate an image using Recraft
async function generateImageWithRecraft(prompt, index, options = {}) {
  const config = SERVICE_CONFIG[IMAGE_SERVICES.RECRAFT];
  let retryCount = 0;

  // Check if model is configured
  if (!config || !config.model) {
    throw new Error('Recraft model not configured. Please check SERVICE_CONFIG.');
  }

  while (retryCount < config.maxRetries) {
    try {
      if (index > 0) {
        await new Promise(resolve => setTimeout(resolve, config.baseDelay * (index % 3)));
      }

      const response = await replicate.run(config.model, {
        input: {
          prompt: prompt,
          negative_prompt: options.negative_prompt || "",
          ...options
        }
      });

      // Check if response is valid
      if (!response || !Array.isArray(response) || response.length === 0) {
        throw new Error('Invalid response from Recraft API');
      }

      return response[0];
    } catch (error) {
      retryCount++;
      console.error(`Recraft API error (attempt ${retryCount}/${config.maxRetries}):`, error.message);

      if (retryCount >= config.maxRetries) {
        throw new Error(`Recraft API failed after ${config.maxRetries} attempts: ${error.message}`);
      }

      await new Promise(resolve => setTimeout(resolve, config.baseDelay));
    }
  }
}

// Generate an image using GPT Image 1
async function generateImageWithGPTImage1(prompt, index, options = {}) {
  const config = SERVICE_CONFIG[IMAGE_SERVICES.GPT_IMAGE_1];
  let retryCount = 0;

  // Check if model is configured
  if (!config || !config.model) {
    throw new Error('GPT Image 1 model not configured. Please check SERVICE_CONFIG.');
  }

  while (retryCount < config.maxRetries) {
    try {
      if (index > 0) {
        await new Promise(resolve => setTimeout(resolve, config.baseDelay * (index % 3)));
      }

      const response = await replicate.run(config.model, {
        input: {
          prompt: prompt,
          negative_prompt: options.negative_prompt || "",
          ...options,
          openai_api_key: process.env.OPENAI_API_KEY // Ensure API key is passed
        }
      });
      console.log(`GPT Image 1 request input:`, {
        prompt: prompt,
        negative_prompt: options.negative_prompt || "",
        ...options,
        openai_api_key: process.env.OPENAI_API_KEY ? 'sk-proj-...' : 'NOT SET'
      });
      console.log(`GPT Image 1 response type:`, Array.isArray(response) ? `Array with ${response.length} items` : typeof response);
      console.log(`GPT Image 1 response[0] type:`, typeof response[0]);
      console.log(`GPT Image 1 response[0] constructor:`, response[0]?.constructor?.name);
      console.log(`GPT Image 1 response[0] toString:`, typeof response[0]?.toString === 'function' ? response[0].toString() : 'No toString method');


      // Check if response is valid
      if (!response || !Array.isArray(response) || response.length === 0) {
        throw new Error('Invalid response from GPT Image 1 API');
      } const imageOutput = response[0];
      console.log(`Image output type: ${typeof imageOutput}, constructor: ${imageOutput?.constructor?.name}`);

      // Handle different response types
      if (typeof imageOutput === 'string') {
        // If it's already a URL or base64 string, return it
        console.log('Image output is a string');
        return imageOutput;
      } else if (typeof imageOutput === 'function') {
        // This might be a wrapped ReadableStream or some other function
        console.log('Image output is a function - attempting to call it...');
        try {
          const result = imageOutput();
          console.log(`Function result type: ${typeof result}, constructor: ${result?.constructor?.name}`);
          if (result && typeof result === 'object' && result.constructor?.name === 'ReadableStream') {
            return result; // Return the stream to be handled by convertImageToBuffer
          } else if (typeof result === 'string') {
            return result;
          } else {
            throw new Error(`Function returned unexpected type: ${typeof result}`);
          }
        } catch (funcError) {
          console.error('Error calling image output function:', funcError.message);
          throw new Error(`Unable to process function-type image output: ${funcError.message}`);
        }
      } else if (imageOutput && typeof imageOutput === 'object' && imageOutput.constructor?.name === 'ReadableStream') {
        // Handle ReadableStream response
        console.log('Image output is a ReadableStream');
        return imageOutput; // Return the stream to be handled by convertImageToBuffer
      } else if (imageOutput && typeof imageOutput === 'object' && imageOutput.constructor?.name === 'FileOutput') {
        // Handle FileOutput response from Replicate
        console.log('Image output is a FileOutput');        // FileOutput objects typically have a url property or toString method
        if (imageOutput.url) {
          const url = typeof imageOutput.url === 'function' ? imageOutput.url() : imageOutput.url;
          console.log('FileOutput URL:', url);
          // If the URL is an object (URL class), convert it to string
          const urlString = typeof url === 'object' && url.href ? url.href : url.toString();
          console.log('FileOutput URL string:', urlString);
          return urlString;
        } else if (typeof imageOutput.toString === 'function') {
          const url = imageOutput.toString();
          console.log('FileOutput toString URL:', url);
          return url;
        } else {
          throw new Error('FileOutput object has no accessible URL');
        }
      } else if (imageOutput && imageOutput.url) {
        // If it has a URL property, return that
        console.log('Image output has URL property');
        // Check if url is a function (getter) and call it, or just return it as a string
        const url = typeof imageOutput.url === 'function' ? imageOutput.url() : imageOutput.url;
        console.log('Extracted URL:', url);
        return url;
      } else {
        console.error('Unexpected image output:', {
          type: typeof imageOutput,
          constructor: imageOutput?.constructor?.name,
          keys: imageOutput && typeof imageOutput === 'object' ? Object.keys(imageOutput) : 'N/A',
          isCallable: typeof imageOutput === 'function',
          hasGetReader: imageOutput && typeof imageOutput.getReader === 'function'
        });
        throw new Error(`Unexpected image output format: ${typeof imageOutput}, constructor: ${imageOutput?.constructor?.name}`);
      }
    } catch (error) {
      retryCount++;
      console.error(`GPT Image 1 API error (attempt ${retryCount}/${config.maxRetries}):`, error.message);

      if (retryCount >= config.maxRetries) {
        throw new Error(`GPT Image 1 API failed after ${config.maxRetries} attempts: ${error.message}`);
      }

      await new Promise(resolve => setTimeout(resolve, config.baseDelay));
    }
  }
}

// Upscale image using Topaz
async function upscaleImageWithTopaz(base64Image) {
  try {
    const response = await replicate.run(
      "nightmareai/real-esrgan:42fed1c4974146d4d2414e2be2c5277e7dbf05cb79a4f6a6fff8c0423f7b3561",
      {
        input: {
          image: base64Image,
          scale: 2
        }
      }
    );

    return response;
  } catch (error) {
    console.error('Topaz upscaling error:', error);
    throw error;
  }
}

// Convert 16-bit PNG to 8-bit PNG
async function convertTo8BitPng(inputPath, outputPath) {
  try {
    const image = sharp(inputPath);
    const metadata = await image.metadata();

    if (metadata.depth === 16) {
      await image
        .png({ compressionLevel: 9, force: true })
        .toFile(outputPath);
      console.log(`Converted 16-bit PNG to 8-bit: ${outputPath}`);
    } else {
      // If already 8-bit, just copy
      await image.toFile(outputPath);
    }
  } catch (error) {
    console.error('Error converting PNG:', error);
    throw error;
  }
}

// Legacy Print API endpoint (for standard print jobs)
app.post('/api/print', async (req, res) => {
  try {
    const { orderKey1, sku, quantity } = req.body;

    // Get the PDF from memory using the bookId (orderKey1)
    const pdfData = getPDFFromMemory(orderKey1);
    if (!pdfData) {
      return res.status(404).json({
        error: 'PDF not found',
        message: 'The PDF file may have been cleaned up or not yet generated. Please regenerate the book.',
        bookId: orderKey1,
        availableBooks: getAllPDFsFromMemory().map(pdf => pdf.id)
      });
    }

    // Prepare the print job data with the PDF file
    const printJobData = {
      orderKey1,
      sku,
      quantity,
      duplex: 'false', // Default to single-sided for books
      file: {
        buffer: pdfData.buffer,
        originalname: pdfData.filename,
        mimetype: 'application/pdf'
      }
    };

    const result = await printingService.submitPrintJob(printJobData);
    res.json(result);
  } catch (error) {
    console.error('Error in legacy print endpoint:', error);
    res.status(500).json({
      error: 'Failed to submit print job',
      details: error.response?.data || error.message
    });
  }
});

// Alexanders Print API endpoints
app.post('/api/print-order', async (req, res) => {
  try {
    const orderData = req.body;
    const result = await printingService.submitPhotobookOrder(orderData);
    res.json(result);
  } catch (error) {
    console.error('Error in photobook print endpoint:', error);
    res.status(500).json({
      error: 'Failed to submit photobook order',
      details: error.response?.data || error.message
    });
  }
});

app.get('/api/print-status/:orderKey', async (req, res) => {
  try {
    const { orderKey } = req.params;
    const result = await printingService.getOrderStatus(orderKey);
    res.json(result);
  } catch (error) {
    console.error('Error getting order status:', error);
    res.status(500).json({
      error: 'Failed to get order status',
      details: error.response?.data || error.message
    });
  }
});

// Webhook endpoints for Alexanders API
app.put('/printing/:orderKey1', async (req, res) => {
  try {
    const { orderKey1 } = req.params;
    const statusData = req.body;

    // Verify API key
    const apiKey = req.headers['x-api-key'];
    if (apiKey !== process.env.ALEXANDERS_API_KEY) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const result = await printingService.handlePrintingStatusUpdate(orderKey1, statusData);
    res.json(result);
  } catch (error) {
    console.error('Error handling printing status update:', error);
    res.status(500).json({ error: 'Failed to process printing status update' });
  }
});

app.put('/shipped/:orderKey1', async (req, res) => {
  try {
    const { orderKey1 } = req.params;
    const shipmentData = req.body;

    // Verify API key
    const apiKey = req.headers['x-api-key'];
    if (apiKey !== process.env.ALEXANDERS_API_KEY) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const result = await printingService.handleShipmentUpdate(orderKey1, shipmentData);
    res.json(result);
  } catch (error) {
    console.error('Error handling shipment update:', error);
    res.status(500).json({ error: 'Failed to process shipment update' });
  }
});

app.put('/error/:orderKey1', async (req, res) => {
  try {
    const { orderKey1 } = req.params;
    const errorData = req.body;

    // Verify API key
    const apiKey = req.headers['x-api-key'];
    if (apiKey !== process.env.ALEXANDERS_API_KEY) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const result = await printingService.handleErrorUpdate(orderKey1, errorData);
    res.json(result);
  } catch (error) {
    console.error('Error handling error update:', error);
    res.status(500).json({ error: 'Failed to process error update' });
  }
});

app.delete('/api/print-order/:orderKey', async (req, res) => {
  try {
    const { orderKey } = req.params;
    const { apiType = 'alexanders' } = req.query;
    const result = await printingService.cancelOrder(orderKey, apiType);
    res.json(result);
  } catch (error) {
    console.error('Error canceling order:', error);
    res.status(500).json({
      error: 'Failed to cancel order',
      details: error.response?.data || error.message
    });
  }
});

// Order tracking endpoints
app.get('/api/orders', async (req, res) => {
  try {
    const orders = await orderTrackingService.getAllOrders();
    res.json(orders);
  } catch (error) {
    console.error('Error getting orders:', error);
    res.status(500).json({ error: 'Failed to get orders' });
  }
});

app.get('/api/orders/:orderKey', async (req, res) => {
  try {
    const { orderKey } = req.params;
    const order = await orderTrackingService.getOrderWithDetails(orderKey);
    if (!order) {
      return res.status(404).json({ error: 'Order not found' });
    }
    res.json(order);
  } catch (error) {
    console.error('Error getting order details:', error);
    res.status(500).json({ error: 'Failed to get order details' });
  }
});

// Check if PDF is available for download
app.get('/api/pdf-status/:bookId', (req, res) => {
  const bookId = req.params.bookId;
  console.log(`PDF status check for book ID: ${bookId}`);

  // Check memory storage (primary for Heroku)
  const pdfData = getPDFFromMemory(bookId);
  if (pdfData) {
    res.json({
      available: true,
      message: 'PDF is available for download',
      downloadUrl: `/download/${bookId}`,
      timestamp: pdfData.timestamp,
      size: pdfData.size,
      storage: 'memory',
      expiresAt: new Date(pdfData.timestamp + (30 * 60 * 1000)).toISOString()
    });
    return;
  }

  // Check filesystem (for local development)
  const filePath = path.join(__dirname, 'output', bookId, `${bookId}.pdf`);
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    res.json({
      available: true,
      message: 'PDF is available for download',
      downloadUrl: `/download/${bookId}`,
      size: stats.size,
      storage: 'filesystem',
      lastModified: stats.mtime.toISOString()
    });
  } else {
    const availableBooks = getAllPDFsFromMemory();
    res.status(404).json({
      available: false,
      message: 'PDF not found. It may have been cleaned up or not yet generated.',
      bookId: bookId,
      availableBooks: availableBooks.map(pdf => ({
        id: pdf.id,
        timestamp: pdf.timestamp,
        size: pdf.size
      })),
      totalAvailable: availableBooks.length
    });
  }
});

// Memory monitoring endpoint
app.get('/api/memory-status', (req, res) => {
  const memUsage = process.memoryUsage();
  const tempStorageSize = tempFileStorage.size;
  const availablePDFs = getAllPDFsFromMemory();

  res.json({
    memoryUsage: {
      rss: Math.round(memUsage.rss / 1024 / 1024) + ' MB',
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + ' MB',
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + ' MB',
      external: Math.round(memUsage.external / 1024 / 1024) + ' MB'
    },
    tempStorage: {
      pdfsStored: tempStorageSize,
      maxAllowed: 10,
      totalSize: availablePDFs.reduce((sum, pdf) => sum + (pdf.size || 0), 0),
      averageSize: tempStorageSize > 0 ? Math.round(availablePDFs.reduce((sum, pdf) => sum + (pdf.size || 0), 0) / tempStorageSize / 1024) + ' KB' : '0 KB'
    },
    availablePDFs: availablePDFs.map(pdf => ({
      id: pdf.id,
      filename: pdf.filename,
      size: Math.round(pdf.size / 1024) + ' KB',
      age: Math.round((Date.now() - pdf.timestamp) / 1000 / 60) + ' minutes ago',
      downloadUrl: pdf.downloadUrl
    })),
    uptime: Math.round(process.uptime()) + ' seconds'
  });
});

// List all available PDFs
app.get('/api/available-pdfs', (req, res) => {
  const availablePDFs = getAllPDFsFromMemory();

  res.json({
    total: availablePDFs.length,
    pdfs: availablePDFs.map(pdf => ({
      id: pdf.id,
      filename: pdf.filename,
      size: Math.round(pdf.size / 1024) + ' KB',
      timestamp: pdf.timestamp,
      age: Math.round((Date.now() - pdf.timestamp) / 1000 / 60) + ' minutes ago',
      downloadUrl: pdf.downloadUrl,
      expiresAt: new Date(pdf.timestamp + (30 * 60 * 1000)).toISOString()
    }))
  });
});

// Serve the output directory for PDF downloads
app.use('/output', express.static(path.join(__dirname, 'output')));

/**
 * GET /api/books
 * Returns a list of generated books (PDFs) with metadata from book_report.csv
 */
app.get('/api/books', async (req, res) => {
  try {
    const outputDir = path.join(__dirname, 'output');
    const csvPath = path.join(outputDir, 'book_report.csv');

    // Get PDFs from memory storage (primary for Heroku)
    const memoryPDFs = getAllPDFsFromMemory();

    // Read all PDF files in output directory (for local development)
    let filesystemPDFs = [];
    try {
      const files = await fs.promises.readdir(outputDir);
      const pdfFiles = files.filter(f => f.endsWith('.pdf'));
      filesystemPDFs = pdfFiles.map(filename => ({
        id: filename.replace('.pdf', ''),
        filename,
        downloadUrl: `/output/${filename}`,
        storage: 'filesystem'
      }));
    } catch (error) {
      console.log('No output directory found (normal for Heroku)');
    }

    // Read and parse CSV if it exists
    let booksMeta = {};
    try {
      const csvData = await fs.promises.readFile(csvPath, 'utf8');
      const lines = csvData.split(/\r?\n/).filter(Boolean);
      const headers = lines[0].split(',');

      for (let i = 1; i < lines.length; i++) {
        // Handle quoted titles with commas
        const match = lines[i].match(/^(\d+),"([^"]+)",([^,]+),([\d.]+),(\d+),(.+)$/);
        if (match) {
          const [_, id, title, imageService, genTime, numPages, generatedAt] = match;
          booksMeta[id] = {
            id,
            title,
            imageService,
            genTime: parseFloat(genTime),
            numPages: parseInt(numPages),
            generatedAt
          };
        }
      }
    } catch (error) {
      console.log('No book report CSV found');
    }

    // Combine memory and filesystem PDFs
    const allPDFs = [...memoryPDFs, ...filesystemPDFs];

    // Add metadata to each PDF
    const booksWithMeta = allPDFs.map(pdf => ({
      ...pdf,
      ...booksMeta[pdf.id]
    }));

    res.json({
      total: booksWithMeta.length,
      books: booksWithMeta
    });

  } catch (error) {
    console.error('Error getting books:', error);
    res.status(500).json({ error: 'Failed to get books' });
  }
});

// Helper function to ensure image format is compatible with PDFKit
async function ensurePDFCompatibleFormat(buffer) {
  try {
    // Check if it's WebP format
    const webpHeader = Buffer.from('RIFF', 'ascii');
    if (buffer.length >= 12 &&
      buffer.subarray(0, 4).equals(webpHeader) &&
      buffer.subarray(8, 12).toString('ascii') === 'WEBP') {

      console.log('Detected WebP format, converting to PNG...');

      // Convert WebP to PNG using sharp
      const pngBuffer = await sharp(buffer)
        .png({ compressionLevel: 6, quality: 90 })
        .toBuffer();

      console.log(`Converted WebP (${buffer.length} bytes) to PNG (${pngBuffer.length} bytes)`);
      return pngBuffer;
    }

    // For other formats (PNG, JPEG), return as-is
    console.log('Image format is already PDF-compatible');
    return buffer;

  } catch (error) {
    console.error('Error checking/converting image format:', error.message);
    // If conversion fails, try to return original buffer
    return buffer;
  }
}

// Start the server
app.listen(port, () => {
  console.log(`Server is running on port ${port}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Developer mode: ${DEV_MODE}`);
});
