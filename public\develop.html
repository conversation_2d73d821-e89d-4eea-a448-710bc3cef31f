<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Children's Book Generator - Developer Mode</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Arial', sans-serif;
        }
        .container {
            max-width: 800px;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-top: 50px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #4361ee;
        }
        .dev-mode-banner {
            background-color: #ffd700;
            color: #000;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .loading {
            display: none;
        }
        .form-control:disabled {
            background-color: #e9ecef;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="dev-mode-banner">
            🚀 Developer Mode Active - Using Default Values
        </div>
        <div class="header">
            <h1>AI Children's Book Generator</h1>
            <p class="lead">Developer Mode - Quick Book Generation</p>
        </div>

        <form id="bookForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="childName" class="form-label">Child's Name</label>
                        <input type="text" class="form-control" id="childName" name="childName" value="Joseph" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="childAge" class="form-label">Child's Age</label>
                        <input type="number" class="form-control" id="childAge" name="childAge" value="4" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="childGender" class="form-label">Child's Gender</label>
                        <select class="form-select" id="childGender" name="childGender" disabled>
                            <option value="boy" selected>Boy</option>
                            <option value="girl">Girl</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="childInterests" class="form-label">Child's Interests</label>
                        <input type="text" class="form-control" id="childInterests" name="childInterests" value="dinosaurs" disabled>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="childHairColor" class="form-label">Hair Color</label>
                        <input type="text" class="form-control" id="childHairColor" name="childHairColor" value="brown" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="childHairStyle" class="form-label">Hair Style</label>
                        <input type="text" class="form-control" id="childHairStyle" name="childHairStyle" value="short" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="childEyeColor" class="form-label">Eye Color</label>
                        <input type="text" class="form-control" id="childEyeColor" name="childEyeColor" value="brown" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="childSkinTone" class="form-label">Skin Tone</label>
                        <input type="text" class="form-control" id="childSkinTone" name="childSkinTone" value="fair" disabled>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="imageService" class="form-label">Image Generation Service</label>
                <select class="form-select" id="imageService" name="imageService">
                    <option value="dall-e">DALL-E</option>
                    <option value="midjourney">Midjourney</option>
                    <option value="pruna">Pruna</option>
                </select>
            </div>
            <div class="text-center">
                <button type="submit" class="btn btn-primary">Generate Book</button>
            </div>
        </form>
        
        <div id="loading" class="loading text-center mt-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Generating your book... This may take a few minutes.</p>
        </div>
        
        <div id="result" class="mt-4 text-center"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const bookForm = document.getElementById('bookForm');
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            
            bookForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // Hide form and show loading
                bookForm.style.display = 'none';
                loading.style.display = 'block';
                result.innerHTML = '';
                
                try {
                    const formData = new FormData(bookForm);
                    const data = Object.fromEntries(formData.entries());
                    
                    const response = await fetch('/generate-book', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data),
                    });
                    
                    const responseData = await response.json();
                    
                    if (responseData.success) {
                        result.innerHTML = `
                            <div class="alert alert-success">
                                ${responseData.message}
                                <br>
                                <a href="${responseData.bookPath}" class="btn btn-success mt-2">Download Book</a>
                            </div>
                        `;
                    } else {
                        throw new Error(responseData.error || 'Failed to generate book');
                    }
                } catch (error) {
                    result.innerHTML = `
                        <div class="alert alert-danger">
                            Error: ${error.message}
                        </div>
                    `;
                } finally {
                    loading.style.display = 'none';
                    bookForm.style.display = 'block';
                }
            });
        });
    </script>
</body>
</html> 