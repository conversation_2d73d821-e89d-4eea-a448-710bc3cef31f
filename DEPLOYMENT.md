# Heroku Deployment Guide

## Prerequisites

1. Install Heroku CLI: https://devcenter.heroku.com/articles/heroku-cli
2. Create a Heroku account: https://signup.heroku.com/
3. Make sure you have Node.js and npm installed

## Important: Heroku Timeout Handling

**Free Heroku dynos have a 30-second timeout limit.** Book generation can take 2-5 minutes, so the app now uses async processing:

- Book generation starts immediately and returns a job ID
- Progress is tracked via polling
- No more timeout errors!

### Testing Async Generation

Visit `/test-async` on your deployed app to test the new async functionality.

## Deployment Steps

### 1. Login to Heroku
```bash
heroku login
```

### 2. Create a new Heroku app
```bash
heroku create your-app-name
```

### 3. Set up environment variables
```bash
# Required variables
heroku config:set OPENAI_API_KEY=your_openai_api_key
heroku config:set REPLICATE_API_TOKEN=your_replicate_token

# Optional variables for Alexanders Print API
heroku config:set ALEXANDERS_API_KEY=your_alexanders_api_key
heroku config:set ALEXANDERS_API_URL=https://devapi.divvy.systems

# Legacy Divvy API (if needed)
heroku config:set DIVVY_API_KEY=your_divvy_api_key
heroku config:set DIVVY_API_BASE_URL=https://api.divvypress.com
```

### 4. Deploy to Heroku
```bash
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

### 5. Open the app
```bash
heroku open
```

## Environment Variables

### Required
- `OPENAI_API_KEY`: Your OpenAI API key
- `REPLICATE_API_TOKEN`: Your Replicate API token

### Optional (for Alexanders Print API)
- `ALEXANDERS_API_KEY`: Your Alexanders Print API key
- `ALEXANDERS_API_URL`: Alexanders API URL (default: https://devapi.divvy.systems)

### Optional (for legacy Divvy API)
- `DIVVY_API_KEY`: Your Divvy API key
- `DIVVY_API_BASE_URL`: Divvy API base URL

## API Endpoints

### Book Generation
- `POST /generate-book`: Start async book generation (returns job ID)
- `GET /api/book-status/:jobId`: Check generation progress
- `GET /test-async`: Test page for async generation

### PDF Management & Monitoring
- `GET /pdf-monitor`: PDF storage monitoring dashboard
- `GET /download/:bookId`: Download generated PDF
- `GET /api/pdf-status/:bookId`: Check if PDF is available for download
- `GET /api/available-pdfs`: List all PDFs currently in memory
- `GET /api/memory-status`: Monitor memory usage and PDF storage
- `GET /api/books`: List all generated books with metadata

### Print Integration & Order Tracking
- `POST /api/print-order`: Submit a photobook order to Alexanders
- `GET /api/print-status/:orderKey`: Get order status (local + remote)
- `DELETE /api/print-order/:orderKey`: Cancel an order
- `GET /print-monitor`: Print order monitoring dashboard
- `GET /api/orders`: List all tracked orders
- `GET /api/orders/:orderKey`: Get detailed order information

### Webhook Endpoints (for Alexanders API)
- `PUT /printing/:orderKey1`: Handle printing status updates
- `PUT /shipped/:orderKey1`: Handle shipment notifications  
- `PUT /error/:orderKey1`: Handle error notifications

### Legacy Print (Divvy API)
- `POST /api/print`: Submit a standard print job

## Print Order Monitoring System

### Overview
The app now includes a comprehensive print order monitoring system that tracks orders from submission through completion:

- **Permanent Storage**: Uses JSON file storage (works on all platforms)
- **Real-time Updates**: Webhook integration with Alexanders API
- **Order Lifecycle**: Track status, shipments, and errors
- **Dashboard**: Beautiful monitoring interface at `/print-monitor`

### Features
- **Order Tracking**: All orders are stored with metadata
- **Status Updates**: Automatic updates via webhooks
- **Shipment Tracking**: Track carriers, tracking numbers, and costs
- **Error Handling**: Record and display processing errors
- **Order Management**: View, refresh, and cancel orders
- **Statistics**: Overview of order status breakdown

### Storage
- **JSON Files**: Simple, reliable storage in `/data` directory
- **Cross-platform**: Works on Windows, Mac, Linux, and Heroku
- **Persistent**: Data survives server restarts
- **Automatic**: Files created automatically when needed

## Testing

### Test Async Book Generation
```bash
# Visit this URL in your browser
https://your-app-name.herokuapp.com/test-async
```

### Test PDF Monitoring
```bash
# Visit this URL to monitor PDF storage and downloads
https://your-app-name.herokuapp.com/pdf-monitor
```

### Test Print Order Monitoring
```bash
# Visit this URL to monitor print orders
https://your-app-name.herokuapp.com/print-monitor
```

### Test Book Generation API
```bash
curl -X POST https://your-app-name.herokuapp.com/generate-book \
  -H "Content-Type: application/json" \
  -d '{
    "childName": "Test Child",
    "childAge": "5",
    "childGender": "boy",
    "childInterests": "dinosaurs",
    "childHairColor": "brown",
    "childHairStyle": "short",
    "childEyeColor": "blue",
    "childSkinTone": "fair"
  }'
```

### Test Print Order
```bash
curl -X POST https://your-app-name.herokuapp.com/api/print-order \
  -H "Content-Type: application/json" \
  -d '{
    "orderKey1": "test-order-123",
    "bookId": "test-book-456",
    "coverUrl": "https://example.com/cover.pdf",
    "gutsUrl": "https://example.com/content.pdf",
    "sku": "6x9-hardcover-book",
    "quantity": 1,
    "shipping": {
      "shipMethod": "UPSGROUND",
      "address": {
        "name": "John Doe",
        "address1": "123 Main St",
        "city": "New York",
        "state": "NY",
        "postalCode": "10001",
        "countryCode": "US",
        "phoneNumber": "1234567890"
      }
    }
  }'
```

### Test Webhook (for development)
```bash
curl -X PUT https://your-app-name.herokuapp.com/printing/test-order-123 \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: your_alexanders_api_key" \
  -d '{
    "dueDate": "2024-01-15T10:00:00Z"
  }'
```

## Monitoring

### View logs
```bash
heroku logs --tail
```

### Check app status
```bash
heroku ps
```

### Restart the app
```bash
heroku restart
```

## Troubleshooting

### Common Issues

1. **Build fails**: Check that all dependencies are in package.json
2. **Environment variables not set**: Use `heroku config` to verify
3. **Port issues**: The app automatically uses `process.env.PORT`
4. **File upload issues**: Heroku has ephemeral filesystem, files are stored in memory
5. **Timeout errors**: Use the async endpoints (`/test-async`) instead of direct generation
6. **Order tracking issues**: Check that the `/data` directory is writable

### Debug Commands
```bash
# Check environment variables
heroku config

# View recent logs
heroku logs --tail

# Run the app locally with Heroku config
heroku local web
```

## Scaling

### Upgrade to paid dyno for better performance
```bash
heroku ps:type standard-1x
```

### Add more dynos for higher traffic
```bash
heroku ps:scale web=2
```

## Custom Domain (Optional)

```bash
heroku domains:add www.yourdomain.com
```

Then update your DNS to point to the provided CNAME.

## PDF Storage on Heroku

### How PDFs are Stored
Due to Heroku's ephemeral filesystem, PDFs are stored in memory rather than on disk:

- **Memory Storage**: PDFs are stored in application memory for up to 30 minutes
- **Automatic Cleanup**: Old PDFs are automatically removed to prevent memory issues
- **Download URLs**: PDFs are accessible via `/download/:bookId` URLs
- **Monitoring**: Use `/pdf-monitor` to check PDF availability and memory usage

### PDF Lifecycle
1. **Generation**: PDF is created and stored in memory
2. **Download**: Available via `/download/:bookId` for 30 minutes
3. **Cleanup**: Automatically removed after 30 minutes or when memory limit is reached
4. **Regeneration**: Can be regenerated if needed

### Best Practices
- Download PDFs immediately after generation
- Monitor memory usage via `/api/memory-status`
- Use `/pdf-monitor` to debug download issues
- PDFs are not permanently stored - plan accordingly

## Order Tracking Storage

### How Orders are Stored
Orders are stored in JSON files in the `/data` directory:

- **orders.json**: All order records with metadata
- **status_updates.json**: Webhook status updates
- **shipments.json**: Shipping information
- **errors.json**: Error records

### Order Data Lifecycle
1. **Submission**: Order created via `/api/print-order`
2. **Tracking**: Status updates via webhooks
3. **Completion**: Order marked as shipped or cancelled
4. **Persistence**: Data survives server restarts

### Best Practices
- Monitor orders via `/print-monitor`
- Check webhook endpoints are accessible
- Verify API keys are correctly set
- Use order details for troubleshooting 