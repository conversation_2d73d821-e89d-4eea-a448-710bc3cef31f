// Image generation service configuration
const IMAGE_SERVICES = {
    DALL_E: 'dall-e',
    MIDJOURNEY: 'midjourney',
    PRUNA: 'pruna',
    RECRAFT: 'recraft',
    GPT_IMAGE_1: 'gpt-image-1'
};

// Service-specific configurations
const SERVICE_CONFIG = {
    [IMAGE_SERVICES.DALL_E]: {
        model: "openai/dall-e-3",
        maxRetries: 3,
        baseDelay: 2000,
        batchSize: 3,
        batchDelay: 10000
    },
    [IMAGE_SERVICES.MIDJOURNEY]: {
        model: "prompthero/openjourney:9936c2001faa2194a261c01381f90e65261879985476014a0a37a334593a05eb",
        maxRetries: 3,
        baseDelay: 5000,
        batchSize: 1,
        batchDelay: 5000
    },
    [IMAGE_SERVICES.PRUNA]: {
        model: "adirik/flux-lora-brad-pitt:1b40c4a9a96dfebc9b6e0b2def95c5cb0833b9b1f34b9b9b4b9a8b9b6b0b2def",
        maxRetries: 3,
        baseDelay: 3000,
        batchSize: 1,
        batchDelay: 3000
    },
    [IMAGE_SERVICES.RECRAFT]: {
        model: "lucataco/recraft-v3:2c40c8df0fe9b0b6b6e3b5b7b9b1b4b2b6b0b2def",
        maxRetries: 3,
        baseDelay: 3000,
        batchSize: 1,
        batchDelay: 3000
    },
    [IMAGE_SERVICES.GPT_IMAGE_1]: {
        model: "openai/gpt-image-1",
        maxRetries: 3,
        baseDelay: 3000,
        batchSize: 1,
        batchDelay: 3000
    }
};

module.exports = {
    IMAGE_SERVICES,
    SERVICE_CONFIG
}; 