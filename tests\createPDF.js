
const { createPDF } = require('../services/pdfService');

const bookContent = {
  title: 'The Adventures of the Magic Mouse',
  pages: [
    {
      text: 'Once upon a time, in a small village, there lived a mouse named <PERSON><PERSON>. <PERSON><PERSON> was a curious and adventurous mouse who loved to explore the world around him.',
    },
    {
      text: 'One day, <PERSON><PERSON> decided to go on an adventure to find a new home. He packed his bags and set off on a journey to find a new place to live.',
    },
    {
      text: 'As <PERSON><PERSON> traveled, he met a friendly cat named <PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON> offered to show <PERSON><PERSON> the world and help him find a new home.',
    },
    {
      text: 'Together, <PERSON><PERSON> and <PERSON><PERSON><PERSON> explored the world, encountering many exciting adventures and challenges.',
    },
    {
      text: '<PERSON><PERSON> learned that even though he was small, he could accomplish great things with the help of his friends.',
    },
    {
      text: 'In the end, <PERSON><PERSON> found a new home and a new friend in Whiskers. Together, they lived happily ever after.',
    },
  ]
};

const images = [];

createPDF(`output/test-book-${Date.now()}.pdf`, bookContent, images);