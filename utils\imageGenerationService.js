const { IMAGE_SERVICES, SERVICE_CONFIG } = require('./config');
const { Replicate } = require('replicate');
const { OpenAI } = require('openai');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Initialize APIs
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

async function generateImageWithRecraft(prompt, pageNumber) {
  try {
    const response = await axios.post('https://api.recraft.ai/v1/images', {
      prompt,
      style: 'childrens_book',
      aspect_ratio: '4:3',
      num_images: 1
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.REPLICATE_API_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.data || !response.data.images || !response.data.images[0]) {
      throw new Error('Invalid response from Recraft API');
    }

    const imageUrl = response.data.images[0];
    const imagePath = path.join(process.env.OUTPUT_DIR, `page_${pageNumber}.png`);
    
    // Download and save the image
    const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    fs.writeFileSync(imagePath, imageResponse.data);

    return imagePath;
  } catch (error) {
    console.error(`Error generating image with Recraft: ${error.message}`);
    throw error;
  }
}

async function generateImage(prompt, pageNumber, service = IMAGE_SERVICES.DALL_E) {
  const config = SERVICE_CONFIG[service];
  let lastError;

  for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
    try {
      switch (service) {
        case IMAGE_SERVICES.DALL_E:
          return await generateImageWithDalle(prompt, pageNumber);
        case IMAGE_SERVICES.MIDJOURNEY:
          return await generateImageWithMidjourney(prompt, pageNumber);
        case IMAGE_SERVICES.PRUNA:
          return await generateImageWithPruna(prompt, pageNumber);
        case IMAGE_SERVICES.RECRAFT:
          return await generateImageWithRecraft(prompt, pageNumber);
        default:
          throw new Error(`Unsupported image service: ${service}`);
      }
    } catch (error) {
      lastError = error;
      console.error(`Attempt ${attempt} failed: ${error.message}`);
      
      if (attempt < config.maxRetries) {
        const delay = config.baseDelay * Math.pow(2, attempt - 1);
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError || new Error('Failed to generate image after all retries');
} 