const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

class PrintingService {
    constructor() {
        this.baseUrl = process.env.DIVVY_API_BASE_URL || 'https://api.divvy.com';
        this.apiKey = process.env.DIVVY_API_KEY;
        this.uploadUrl = process.env.PDF_UPLOAD_URL || 'https://upload-placeholder.com/api/upload';
    }

    async uploadPdf(pdfBuffer) {
        try {
            const formData = new FormData();
            formData.append('file', pdfBuffer, {
                filename: 'book.pdf',
                contentType: 'application/pdf'
            });

            const response = await axios.post(this.uploadUrl, formData, {
                headers: {
                    ...formData.getHeaders()
                }
            });

            return response.data.fileUrl; // Assuming the API returns { fileUrl: 'https://...' }
        } catch (error) {
            console.error('Error uploading PDF:', error);
            throw error;
        }
    }

    async submitPrintJob(orderData) {
        try {
            // First upload the PDF if provided as a buffer
            let gutsUrl = orderData.gutsUrl;
            let coverUrl = orderData.coverUrl;

            if (orderData.pdfBuffer) {
                gutsUrl = await this.uploadPdf(orderData.pdfBuffer);
            }

            if (orderData.coverBuffer) {
                coverUrl = await this.uploadPdf(orderData.coverBuffer);
            }

            const formData = new FormData();
            
            // Add required fields from the order data
            formData.append('orderKey1', orderData.orderKey1);
            formData.append('photobookItems[0][sku]', orderData.sku);
            formData.append('photobookItems[0][quantity]', orderData.quantity);
            formData.append('photobookItems[0][gutsUrl]', gutsUrl);
            
            // Add optional cover URL if it exists
            if (coverUrl) {
                formData.append('photobookItems[0][coverUrl]', coverUrl);
            }

            // Add optional foil properties if they exist
            if (orderData.foilUrl) {
                formData.append('photobookItems[0][foilUrl]', orderData.foilUrl);
            }
            
            if (orderData.foilColor) {
                formData.append('photobookItems[0][foilColor]', orderData.foilColor);
            }

            const response = await axios.post(`${this.baseUrl}/v1.1/order`, formData, {
                headers: {
                    'X-API-KEY': this.apiKey,
                    ...formData.getHeaders()
                }
            });

            return response.data;
        } catch (error) {
            console.error('Error submitting print job:', error);
            throw error;
        }
    }

    async cancelOrder(orderKey) {
        try {
            const response = await axios.delete(`${this.baseUrl}/v1.1/order/${orderKey}`, {
                headers: {
                    'X-API-KEY': this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error canceling order:', error);
            throw error;
        }
    }
}

module.exports = new PrintingService(); 