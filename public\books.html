<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Generated Books | AI Children's Book Generator</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Arial', sans-serif;
    }
    .container {
      max-width: 900px;
      padding: 30px;
      background: white;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-top: 50px;
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .header h1 {
      color: #4361ee;
    }
    .table thead th {
      background-color: #e9ecef;
    }
    .download-link {
      text-decoration: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Generated Books</h1>
      <p class="lead">View and download all books generated so far.</p>
    </div>
    <div id="booksSection">
      <div class="text-center mb-4" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
      <div id="booksTableWrapper" style="display:none;">
        <table class="table table-striped table-hover">
          <thead>
            <tr>
              <th>Title</th>
              <th>Book ID</th>
              <th>Pages</th>
              <th>Image Service</th>
              <th>Generated At</th>
              <th>Download</th>
              <th>Print</th>
            </tr>
          </thead>
          <tbody id="booksTableBody">
            <!-- Book rows go here -->
          </tbody>
        </table>
      </div>
      <div id="noBooksMsg" class="alert alert-info text-center" style="display:none;">No books found.</div>
    </div>
  </div>
  <script>
    document.addEventListener('DOMContentLoaded', async function() {
      const loadingSpinner = document.getElementById('loadingSpinner');
      const booksTableWrapper = document.getElementById('booksTableWrapper');
      const booksTableBody = document.getElementById('booksTableBody');
      const noBooksMsg = document.getElementById('noBooksMsg');

      try {
        const res = await fetch('/api/books');
        const books = await res.json();
        loadingSpinner.style.display = 'none';
        if (Array.isArray(books) && books.length > 0) {
          booksTableWrapper.style.display = '';
          books.forEach(book => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
              <td>${book.title ? book.title.replace(/</g, '&lt;').replace(/>/g, '&gt;') : ''}</td>
              <td>${book.id}</td>
              <td>${book.numPages || ''}</td>
              <td>${book.imageService || ''}</td>
              <td>${book.generatedAt ? new Date(book.generatedAt).toLocaleString() : ''}</td>
              <td><a class="btn btn-success btn-sm download-link" href="${book.downloadUrl}" target="_blank">Download PDF</a></td>
              <td><a class="btn btn-primary btn-sm" href="print-book.html?id=${book.id}">Send to Printer</a></td>
            `;
            booksTableBody.appendChild(tr);
          });
        } else {
          noBooksMsg.style.display = '';
        }
      } catch (err) {
        loadingSpinner.style.display = 'none';
        noBooksMsg.textContent = 'Failed to load books.';
        noBooksMsg.style.display = '';
      }
    });
  </script>
</body>
</html> 