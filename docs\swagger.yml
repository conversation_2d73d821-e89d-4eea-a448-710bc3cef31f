swagger: '2.0'
info:
  version: 1.0.0
  title: Order Status Update
  description: |
    **Overview**

    Customer will need to implement this API specification in order to receive updates as orders are fulfilled. Please provide the finished endpoint(s) to APA for integration.

    Ideally customer will provide a development/sandbox version of this API as well that can be used during development without interrupting services for the customer.

    **Security**

    Transport security shall use TLS (HTTPS requests).  Securing access to the api will be handled by the api key being sent in the header.
  contact: {}
# host: www.example.com
# basePath: /
securityDefinitions:
  API-KEY:
    type: apiKey
    description: Partner API key provided by <PERSON><PERSON>. Used in header to authorize.
    name: X-API-KEY
    in: header
# schemes:
#   - http
consumes:
  - application/json
produces:
  - application/json
paths:
  '/printing/{orderKey1}':
    put:
      description: Receive updated statuses for the specified order
      summary: Updateprintingstatus
      tags:
        - Printing
      operationId: PrintingByOrderKey1Put
      produces:
        - application/json
      parameters:
        - name: orderKey1
          in: path
          required: true
          type: string
          description: key sent in original request
        - name: body
          in: body
          required: false
          description: ''
          schema:
            $ref: '#/definitions/StatusUpdate'
      responses:
        '200':
          description: OK (success)
        '404':
          description: Not Found (order doesn't exist)
          schema: {}
      security:
        - API-KEY: []
  '/shipped/{orderKey1}':
    put:
      description: Shipments that was created for the order.
      summary: OrderShipped
      tags:
        - Shipped
      operationId: ShippedByOrderKey1Put
      produces:
        - application/json
      parameters:
        - name: orderKey1
          in: path
          required: true
          type: string
          description: Order key sent in original request
        - name: body
          in: body
          required: false
          description: ''
          schema:
            $ref: '#/definitions/Shipment'
      responses:
        '200':
          description: OK
        '404':
          description: Not Found (order doesn't exist)
          schema: {}
      security:
        - API-KEY: []
  '/error/{orderKey1}':
    put:
      description: ''
      summary: Error
      tags:
        - Error
      operationId: ErrorByOrderKey1Put
      produces:
        - application/json
      parameters:
        - name: orderKey1
          in: path
          required: true
          type: string
          description: key sent in original request
        - name: body
          in: body
          required: false
          description: ''
          schema:
            $ref: '#/definitions/Error'
      responses:
        '200':
          description: OK
        '404':
          description: Not Found (order doesn't exist)
          schema: {}
      security:
        - API-KEY: []
definitions:
  StatusUpdate:
    title: StatusUpdate
    type: object
    properties:
      dueDate:
        type: string
        format: date-time
  Shipment:
    title: Shipment
    type: object
    properties:
      shipMethod:
        example: 1st class ground
        type: string
      carrier:
        example: UPS
        type: string
      trackingNumber:
        type: string
      dateShipped:
        type: string
        format: date-time
      cost:
        description: 'This is in cents, so $11.50 = 1150'
        example: 1150
        type: number
        format: double
  Error:
    title: Error
    type: object
    properties:
      itemKey:
        description: 'Unique item identifier (for the order), may not be set if error applies to the entire order'
        type: string
      message:
        description: Message
        example: Failed to download item
        type: string
tags:
  - name: Printing
    description: ''
  - name: Shipped
    description: ''
  - name: Error
    description: ''
# Added by API Auto Mocking Plugin
host: virtserver.swaggerhub.com
basePath: /apa/apipostback.divvy.systems/1.0.0
schemes:
 - https
 - http