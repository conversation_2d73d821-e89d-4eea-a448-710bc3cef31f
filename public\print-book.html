<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Print Book | AI Children's Book Generator</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Arial', sans-serif;
    }
    .container {
      max-width: 600px;
      padding: 30px;
      background: white;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-top: 50px;
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .header h1 {
      color: #4361ee;
    }
    .progress {
      height: 25px;
    }
    .hidden { display: none; }
    .shipping-info {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Send Book to Printer</h1>
      <p class="lead">Upload your generated book to Alexanders Printing Service.</p>
    </div>
    <form id="printForm">
      <div class="mb-3">
        <label for="bookId" class="form-label">Book ID</label>
        <input type="text" class="form-control" id="bookId" name="bookId" readonly>
      </div>
      <div class="mb-3">
        <label for="sku" class="form-label">SKU</label>
        <input type="text" class="form-control" id="sku" name="sku" value="6x9-hardcover-book" required>
        <div class="form-text">Default: 6x9 Hardcover Book</div>
      </div>
      <div class="mb-3">
        <label for="quantity" class="form-label">Quantity</label>
        <input type="number" class="form-control" id="quantity" name="quantity" min="1" value="1" required>
      </div>
      <div class="shipping-info">
        <h5>Shipping Information</h5>
        <p class="mb-0">MEDL Mobile Enterprises, LLC<br>
        8891 Research Dr.<br>
        Irvine, CA 92618<br>
        United States</p>
      </div>
      <button type="submit" class="btn btn-primary w-100">Send to Printer</button>
    </form>
    <div id="progressSection" class="hidden mt-4">
      <h5>Upload Progress</h5>
      <div class="progress mb-2">
        <div class="progress-bar progress-bar-striped progress-bar-animated" id="progressBar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
      </div>
      <div id="statusText"></div>
    </div>
    <div id="resultSection" class="hidden mt-4"></div>
  </div>
  <script>
    // Helper to get query param
    function getQueryParam(name) {
      const url = new URL(window.location.href);
      return url.searchParams.get(name);
    }

    document.addEventListener('DOMContentLoaded', async function() {
      const bookId = getQueryParam('id');
      document.getElementById('bookId').value = bookId || '';
      const progressSection = document.getElementById('progressSection');
      const progressBar = document.getElementById('progressBar');
      const statusText = document.getElementById('statusText');
      const resultSection = document.getElementById('resultSection');
      const printForm = document.getElementById('printForm');

      printForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        resultSection.classList.add('hidden');
        progressSection.classList.remove('hidden');
        progressBar.style.width = '0%';
        progressBar.textContent = '0%';
        statusText.textContent = 'Submitting order...';

        const orderData = {
          orderKey1: bookId,
          sku: document.getElementById('sku').value,
          quantity: document.getElementById('quantity').value
        };

        try {
          const response = await fetch('/api/print', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(orderData)
          });

          progressSection.classList.add('hidden');
          resultSection.classList.remove('hidden');

          if (response.ok) {
            const result = await response.json();
            resultSection.innerHTML = `
              <div class="alert alert-success">
                <h5>Order Submitted Successfully!</h5>
                <p>Order Key: ${result.orderKey || ''}</p>
                <p>Status: ${result.status || 'Submitted'}</p>
                <pre class="mt-3">${JSON.stringify(result, null, 2)}</pre>
              </div>`;
          } else {
            const error = await response.text();
            resultSection.innerHTML = `
              <div class="alert alert-danger">
                <h5>Order Submission Failed</h5>
                <p>${error}</p>
              </div>`;
          }
        } catch (err) {
          progressSection.classList.add('hidden');
          resultSection.classList.remove('hidden');
          resultSection.innerHTML = `
            <div class="alert alert-danger">
              <h5>Error</h5>
              <p>${err.message}</p>
            </div>`;
        }
      });
    });
  </script>
</body>
</html> 