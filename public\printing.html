<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Printing Service</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="file"], input[type="text"], select {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #response {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Printing Service</h1>
    <form id="printingForm">
        <div class="form-group">
            <label for="orderKey">Order Key:</label>
            <input type="text" id="orderKey" name="orderKey" required>
        </div>
        
        <div class="form-group">
            <label for="sku">SKU:</label>
            <input type="text" id="sku" name="sku" value="8x8-hardcover-book" required>
        </div>

        <div class="form-group">
            <label for="quantity">Quantity:</label>
            <input type="number" id="quantity" name="quantity" min="1" value="1" required>
        </div>

        <div id="fileUploadGroup" class="form-group">
            <label for="file">Print File:</label>
            <input type="file" id="file" name="file" accept=".pdf">
        </div>

        <div class="form-group hidden">
            <label for="duplex">Print Type:</label>
            <select id="duplex" name="duplex">
                <option value="true">Double Sided</option>
                <option value="false">Single Sided</option>
            </select>
        </div>

        <button type="submit">Submit Print Job</button>
    </form>

    <div id="response"></div>

    <script>
        // Get bookId from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const bookId = urlParams.get('bookId');

        // Auto-fill form if bookId is present
        if (bookId) {
            document.getElementById('orderKey').value = bookId;
            document.getElementById('fileUploadGroup').classList.add('hidden');
        }

        document.getElementById('printingForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            formData.append('orderKey1', document.getElementById('orderKey').value);
            formData.append('sku', document.getElementById('sku').value);
            formData.append('quantity', document.getElementById('quantity').value);

            // If no bookId, use file upload
            if (!bookId && document.getElementById('file').files.length > 0) {
                formData.append('file', document.getElementById('file').files[0]);
            } else {
                // If bookId is present, use the PDF URL
                formData.append('fileUrl', `/download/${bookId}`);
            }

            try {
                const response = await fetch('/api/print', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                document.getElementById('response').innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('response').innerHTML = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html> 