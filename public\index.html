// public/index.html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Children's Book Generator</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Arial', sans-serif;
    }
    .container {
      max-width: 800px;
      padding: 30px;
      background: white;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-top: 50px;
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .header h1 {
      color: #4361ee;
    }
    .progress-section {
      display: none;
      margin-top: 30px;
    }
    .result-section {
      display: none;
      margin-top: 30px;
      text-align: center;
    }
    .download-btn {
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>AI Children's Book Generator</h1>
      <p class="lead">Create a personalized storybook for your child in under 60 seconds!</p>
    </div>

    <form id="bookForm">
      <div class="mb-3">
        <label for="childName" class="form-label">Child's Name</label>
        <input type="text" class="form-control" id="childName" name="childName" required>
      </div>
      
      <div class="mb-3">
        <label for="childAge" class="form-label">Child's Age</label>
        <input type="number" class="form-control" id="childAge" name="childAge" min="2" max="12" required>
      </div>
      
      <div class="mb-3">
        <label for="childGender" class="form-label">Child's Gender</label>
        <select class="form-select" id="childGender" name="childGender" required>
          <option value="">Select gender</option>
          <option value="boy">Boy</option>
          <option value="girl">Girl</option>
          <option value="non-binary">Non-binary</option>
        </select>
      </div>
      
      <div class="mb-3">
        <label for="childInterests" class="form-label">Child's Interests</label>
        <textarea class="form-control" id="childInterests" name="childInterests" rows="3" placeholder="e.g., dinosaurs, space, ballet, unicorns, superheroes" required></textarea>
      </div>

      <div class="mb-3">
        <label for="childHairColor" class="form-label">Child's Hair Color</label>
        <select class="form-select" id="childHairColor" name="childHairColor" required>
          <option value="">Select hair color</option>
          <option value="black">Black</option>
          <option value="brown">Brown</option>
          <option value="blonde">Blonde</option>
          <option value="red">Red</option>
          <option value="gray">Gray</option>
          <option value="white">White</option>
        </select>
      </div>

      <div class="mb-3">
        <label for="childHairStyle" class="form-label">Child's Hair Style</label>
        <select class="form-select" id="childHairStyle" name="childHairStyle" required>
          <option value="">Select hair style</option>
          <option value="short">Short</option>
          <option value="medium">Medium</option>
          <option value="long">Long</option>
          <option value="curly">Curly</option>
          <option value="straight">Straight</option>
          <option value="braided">Braided</option>
          <option value="ponytail">Ponytail</option>
        </select>
      </div>

      <div class="mb-3">
        <label for="childEyeColor" class="form-label">Child's Eye Color</label>
        <select class="form-select" id="childEyeColor" name="childEyeColor" required>
          <option value="">Select eye color</option>
          <option value="brown">Brown</option>
          <option value="blue">Blue</option>
          <option value="green">Green</option>
          <option value="hazel">Hazel</option>
          <option value="gray">Gray</option>
        </select>
      </div>

      <div class="mb-3">
        <label for="childSkinTone" class="form-label">Child's Skin Tone</label>
        <select class="form-select" id="childSkinTone" name="childSkinTone" required>
          <option value="">Select skin tone</option>
          <option value="very fair">Very Fair</option>
          <option value="fair">Fair</option>
          <option value="medium">Medium</option>
          <option value="olive">Olive</option>
          <option value="tan">Tan</option>
          <option value="brown">Brown</option>
          <option value="dark">Dark</option>
        </select>
      </div>

      <div class="mb-3">
        <label for="imageService" class="form-label">Image Generation Service</label>
        <select class="form-select" id="imageService" required>
          <option value="dall-e">DALL-E 3 (Faster, Good Quality)</option>
          <option value="midjourney">Midjourney (Slower, Best Quality)</option>
          <option value="pruna">Pruna AI (Fast, High Quality)</option>
          <option value="recraft">Recraft AI (Fast, Children's Book Style)</option>
          <option value="gpt-image-1">GPT-Image-1 (Fast, High Quality)</option>
        </select>
        <div class="form-text">Choose your preferred image generation service:
          <ul class="mt-1 mb-0">
            <li>DALL-E 3: Fast generation, good quality illustrations</li>
            <li>Midjourney: Slower generation, highest quality illustrations</li>
            <li>Pruna AI: Fast generation with high quality, optimized for children's book style</li>
            <li>Recraft AI: Fast generation, specifically designed for children's book illustrations</li>
            <li>GPT-Image-1: Fast generation with high quality, supports image-to-image generation</li>
          </ul>
        </div>
      </div>
      
      <button type="submit" class="btn btn-primary w-100">Generate Book</button>
    </form>
    
    <div class="progress-section" id="progressSection">
      <h3 class="text-center mb-3">Creating Your Book</h3>
      <div class="progress" style="height: 25px;">
        <div class="progress-bar progress-bar-striped progress-bar-animated" 
             id="progressBar" 
             role="progressbar" 
             style="width: 0%;" 
             aria-valuenow="0" 
             aria-valuemin="0" 
             aria-valuemax="100">0%</div>
      </div>
      <p class="text-center mt-3" id="statusText">Initializing...</p>
    </div>
    
    <div class="result-section" id="resultSection">
      <div class="alert alert-success">
        <h4 class="alert-heading">Your book is ready!</h4>
        <p id="resultMessage"></p>
      </div>
      <a id="downloadLink" class="btn btn-success download-btn" href="#" target="_blank">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download me-2" viewBox="0 0 16 16">
          <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
          <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
        </svg>
        Download Book (PDF)
      </a>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const bookForm = document.getElementById('bookForm');
      const progressSection = document.getElementById('progressSection');
      const progressBar = document.getElementById('progressBar');
      const statusText = document.getElementById('statusText');
      const resultSection = document.getElementById('resultSection');
      const resultMessage = document.getElementById('resultMessage');
      const downloadLink = document.getElementById('downloadLink');
      
      bookForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const childName = document.getElementById('childName').value;
        const childAge = document.getElementById('childAge').value;
        const childGender = document.getElementById('childGender').value;
        const childInterests = document.getElementById('childInterests').value;
        const childHairColor = document.getElementById('childHairColor').value;
        const childHairStyle = document.getElementById('childHairStyle').value;
        const childEyeColor = document.getElementById('childEyeColor').value;
        const childSkinTone = document.getElementById('childSkinTone').value;
        const imageService = document.getElementById('imageService').value;
        
        // Show progress section
        bookForm.style.display = 'none';
        progressSection.style.display = 'block';
        
        // Simulate progress updates
        let progress = 0;
        const progressInterval = setInterval(() => {
          if (progress < 90) {
            progress += 5;
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
            progressBar.textContent = `${progress}%`;
            
            // Update status text based on progress
            if (progress < 20) {
              statusText.textContent = 'Creating your personalized story...';
            } else if (progress < 40) {
              statusText.textContent = 'Designing characters based on interests...';
            } else if (progress < 60) {
              statusText.textContent = 'Generating illustrations...';
            } else {
              statusText.textContent = 'Compiling your book...';
            }
          }
        }, 3000 / 18); // Distribute updates over ~50 seconds
        
        try {
          const response = await fetch('/generate-book', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              childName,
              childAge,
              childGender,
              childInterests,
              childHairColor,
              childHairStyle,
              childEyeColor,
              childSkinTone,
              imageService
            }),
          });
          
          const data = await response.json();
          
          if (data.success) {
            // Set progress to 100%
            clearInterval(progressInterval);
            progressBar.style.width = '100%';
            progressBar.setAttribute('aria-valuenow', 100);
            progressBar.textContent = '100%';
            statusText.textContent = 'Book complete!';
            
            // Show result section
            setTimeout(() => {
              progressSection.style.display = 'none';
              resultSection.style.display = 'block';
              resultMessage.textContent = data.message;
              downloadLink.href = data.bookPath;
            }, 1000);
          } else {
            throw new Error(data.error || 'Failed to generate book');
          }
        } catch (error) {
          clearInterval(progressInterval);
          statusText.textContent = 'Error: ' + error.message;
          progressBar.classList.remove('bg-primary');
          progressBar.classList.add('bg-danger');
        }
      });
    });
  </script>
</body>
</html>