{"name": "ai-childrens-book-generator", "version": "1.0.0", "description": "Generate personalized children's books with AI-generated text and images", "main": "app.js", "engines": {"node": "20.x", "npm": "10.x"}, "scripts": {"start": "node app.js", "dev": "nodemon app.js"}, "keywords": ["ai", "children", "book", "generator", "openai", "pdf"], "author": "", "license": "MIT", "dependencies": {"@fontsource/league-spartan": "^5.2.5", "axios": "^1.9.0", "body-parser": "^1.20.2", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.2", "heroku": "^10.10.1", "multer": "^1.4.5-lts.2", "node-fetch": "^3.3.2", "openai": "^4.20.1", "pdf-lib": "^1.17.1", "pdfkit": "^0.14.0", "pngjs": "^7.0.0", "replicate": "^1.0.1", "sharp": "^0.34.1", "svg-to-pdfkit": "^0.1.8"}, "devDependencies": {"nodemon": "^3.0.1", "playwright": "^1.53.1"}}