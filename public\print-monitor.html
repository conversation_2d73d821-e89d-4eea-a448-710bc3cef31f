<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Order Monitor - ForeverBook</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #00A79D 0%, #008B8B 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: none;
            font-size: 1em;
            color: #6c757d;
        }

        .nav-tab.active {
            background: white;
            color: #00A79D;
            border-bottom: 3px solid #00A79D;
        }

        .nav-tab:hover {
            background: #e9ecef;
        }

        .tab-content {
            padding: 30px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-card h3 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .stat-card p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .orders-table th,
        .orders-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .orders-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .orders-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-processing {
            background: #cce5ff;
            color: #004085;
        }

        .status-printing {
            background: #d4edda;
            color: #155724;
        }

        .status-shipped {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .status-cancelled {
            background: #e2e3e5;
            color: #383d41;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #00A79D;
            color: white;
        }

        .btn-primary:hover {
            background: #008B8B;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .order-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .order-details h3 {
            color: #495057;
            margin-bottom: 15px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .detail-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #00A79D;
        }

        .detail-item h4 {
            color: #6c757d;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .detail-item p {
            color: #495057;
            font-weight: 500;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .refresh-btn {
            background: #00A79D;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin-bottom: 20px;
        }

        .refresh-btn:hover {
            background: #008B8B;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .orders-table {
                font-size: 0.9em;
            }

            .orders-table th,
            .orders-table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Print Order Monitor</h1>
            <p>Track the status of your ForeverBook print orders</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">Overview</button>
            <button class="nav-tab" onclick="showTab('orders')">All Orders</button>
            <button class="nav-tab" onclick="showTab('details')">Order Details</button>
        </div>

        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <button class="refresh-btn" onclick="loadOverview()">Refresh Data</button>

            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalOrders">-</h3>
                    <p>Total Orders</p>
                </div>
                <div class="stat-card">
                    <h3 id="pendingOrders">-</h3>
                    <p>Pending</p>
                </div>
                <div class="stat-card">
                    <h3 id="processingOrders">-</h3>
                    <p>Processing</p>
                </div>
                <div class="stat-card">
                    <h3 id="shippedOrders">-</h3>
                    <p>Shipped</p>
                </div>
            </div>

            <div id="overviewContent">
                <div class="loading">Loading overview data...</div>
            </div>
        </div>

        <!-- Orders Tab -->
        <div id="orders" class="tab-content">
            <button class="refresh-btn" onclick="loadOrders()">Refresh Orders</button>

            <div id="ordersContent">
                <div class="loading">Loading orders...</div>
            </div>
        </div>

        <!-- Order Details Tab -->
        <div id="details" class="tab-content">
            <div class="order-details">
                <h3>Order Details</h3>
                <p>Select an order from the Orders tab to view detailed information.</p>
            </div>
        </div>
    </div>

    <script>
        let currentOrders = [];
        let selectedOrder = null;

        // Tab switching
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked nav tab
            event.target.classList.add('active');

            // Load data for the tab
            if (tabName === 'overview') {
                loadOverview();
            } else if (tabName === 'orders') {
                loadOrders();
            }
        }

        // Load overview data
        async function loadOverview() {
            try {
                const response = await fetch('/api/orders');
                const orders = await response.json();

                currentOrders = orders;

                // Calculate stats
                const total = orders.length;
                const pending = orders.filter(o => o.status === 'pending').length;
                const processing = orders.filter(o => o.status === 'processing' || o.status === 'printing').length;
                const shipped = orders.filter(o => o.status === 'shipped').length;

                // Update stats
                document.getElementById('totalOrders').textContent = total;
                document.getElementById('pendingOrders').textContent = pending;
                document.getElementById('processingOrders').textContent = processing;
                document.getElementById('shippedOrders').textContent = shipped;

                // Show recent orders
                const recentOrders = orders.slice(0, 5);
                displayRecentOrders(recentOrders);

            } catch (error) {
                console.error('Error loading overview:', error);
                document.getElementById('overviewContent').innerHTML =
                    '<div class="error">Error loading overview data. Please try again.</div>';
            }
        }

        // Display recent orders
        function displayRecentOrders(orders) {
            if (orders.length === 0) {
                document.getElementById('overviewContent').innerHTML =
                    '<div class="empty-state"><h3>No Orders Yet</h3><p>Orders will appear here once they are submitted.</p></div>';
                return;
            }

            const html = `
                <h3 style="margin-bottom: 20px; color: #495057;">Recent Orders</h3>
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>Order Key</th>
                            <th>Book ID</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${orders.map(order => `
                            <tr>
                                <td>${order.orderKey1}</td>
                                <td>${order.bookId || 'N/A'}</td>
                                <td><span class="status-badge status-${order.status}">${order.status}</span></td>
                                <td>${new Date(order.createdAt).toLocaleDateString()}</td>
                                <td>
                                    <button class="btn btn-primary" onclick="viewOrderDetails('${order.orderKey1}')">View</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            document.getElementById('overviewContent').innerHTML = html;
        }

        // Load all orders
        async function loadOrders() {
            try {
                const response = await fetch('/api/orders');
                const orders = await response.json();

                currentOrders = orders;
                displayAllOrders(orders);

            } catch (error) {
                console.error('Error loading orders:', error);
                document.getElementById('ordersContent').innerHTML =
                    '<div class="error">Error loading orders. Please try again.</div>';
            }
        }

        // Display all orders
        function displayAllOrders(orders) {
            if (orders.length === 0) {
                document.getElementById('ordersContent').innerHTML =
                    '<div class="empty-state"><h3>No Orders Found</h3><p>Orders will appear here once they are submitted.</p></div>';
                return;
            }

            const html = `
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>Order Key</th>
                            <th>Book ID</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${orders.map(order => `
                            <tr>
                                <td>${order.orderKey1}</td>
                                <td>${order.bookId || 'N/A'}</td>
                                <td><span class="status-badge status-${order.status}">${order.status}</span></td>
                                <td>${new Date(order.createdAt).toLocaleDateString()}</td>
                                <td>${new Date(order.updatedAt).toLocaleDateString()}</td>
                                <td>
                                    <button class="btn btn-primary" onclick="viewOrderDetails('${order.orderKey1}')">View</button>
                                    <button class="btn btn-secondary" onclick="refreshOrderStatus('${order.orderKey1}')">Refresh</button>
                                    <button class="btn btn-danger" onclick="cancelOrder('${order.orderKey1}')">Cancel</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            document.getElementById('ordersContent').innerHTML = html;
        }

        // View order details
        async function viewOrderDetails(orderKey) {
            try {
                const response = await fetch(`/api/orders/${orderKey}`);
                const order = await response.json();

                selectedOrder = order;
                displayOrderDetails(order);

                // Switch to details tab
                showTab('details');

            } catch (error) {
                console.error('Error loading order details:', error);
                alert('Error loading order details. Please try again.');
            }
        }

        // Display order details
        function displayOrderDetails(order) {
            const html = `
                <div class="order-details">
                    <h3>Order: ${order.orderKey1}</h3>
                    
                    <div class="detail-grid">
                        <div class="detail-item">
                            <h4>Status</h4>
                            <p><span class="status-badge status-${order.status}">${order.status}</span></p>
                        </div>
                        <div class="detail-item">
                            <h4>Book ID</h4>
                            <p>${order.bookId || 'N/A'}</p>
                        </div>
                        <div class="detail-item">
                            <h4>Created</h4>
                            <p>${new Date(order.createdAt).toLocaleString()}</p>
                        </div>
                        <div class="detail-item">
                            <h4>Updated</h4>
                            <p>${new Date(order.updatedAt).toLocaleString()}</p>
                        </div>
                    </div>

                    ${order.statusUpdates && order.statusUpdates.length > 0 ? `
                        <h4 style="margin-top: 30px; color: #495057;">Status Updates</h4>
                        <div class="detail-grid">
                            ${order.statusUpdates.map(update => `
                                <div class="detail-item">
                                    <h4>${update.updateType}</h4>
                                    <p>${new Date(update.createdAt).toLocaleString()}</p>
                                    <small>${JSON.stringify(update.statusData)}</small>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}

                    ${order.shipments && order.shipments.length > 0 ? `
                        <h4 style="margin-top: 30px; color: #495057;">Shipments</h4>
                        <div class="detail-grid">
                            ${order.shipments.map(shipment => `
                                <div class="detail-item">
                                    <h4>Shipment</h4>
                                    <p>${shipment.carrier} - ${shipment.trackingNumber}</p>
                                    <small>Shipped: ${new Date(shipment.dateShipped).toLocaleDateString()}</small>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}

                    ${order.errors && order.errors.length > 0 ? `
                        <h4 style="margin-top: 30px; color: #495057;">Errors</h4>
                        <div class="detail-grid">
                            ${order.errors.map(error => `
                                <div class="detail-item">
                                    <h4>Error</h4>
                                    <p>${error.message}</p>
                                    <small>${new Date(error.createdAt).toLocaleString()}</small>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}

                    <div style="margin-top: 30px;">
                        <button class="btn btn-primary" onclick="refreshOrderStatus('${order.orderKey1}')">Refresh Status</button>
                        <button class="btn btn-danger" onclick="cancelOrder('${order.orderKey1}')">Cancel Order</button>
                    </div>
                </div>
            `;

            document.getElementById('details').innerHTML = html;
        }

        // Refresh order status
        async function refreshOrderStatus(orderKey) {
            try {
                const response = await fetch(`/api/print-status/${orderKey}`);
                const result = await response.json();

                // Reload orders to get updated data
                await loadOrders();

                if (selectedOrder && selectedOrder.orderKey1 === orderKey) {
                    await viewOrderDetails(orderKey);
                }

                alert('Order status refreshed successfully!');

            } catch (error) {
                console.error('Error refreshing order status:', error);
                alert('Error refreshing order status. Please try again.');
            }
        }

        // Cancel order
        async function cancelOrder(orderKey) {
            if (!confirm('Are you sure you want to cancel this order?')) {
                return;
            }

            try {
                const response = await fetch(`/api/print-order/${orderKey}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    alert('Order cancelled successfully!');
                    await loadOrders();

                    if (selectedOrder && selectedOrder.orderKey1 === orderKey) {
                        await viewOrderDetails(orderKey);
                    }
                } else {
                    const error = await response.json();
                    alert(`Error cancelling order: ${error.error}`);
                }

            } catch (error) {
                console.error('Error cancelling order:', error);
                alert('Error cancelling order. Please try again.');
            }
        }

        // Auto-refresh every 30 seconds
        setInterval(() => {
            if (document.querySelector('.tab-content.active').id === 'overview') {
                loadOverview();
            } else if (document.querySelector('.tab-content.active').id === 'orders') {
                loadOrders();
            }
        }, 30000);

        // Load initial data
        document.addEventListener('DOMContentLoaded', () => {
            loadOverview();
        });
    </script>
</body>

</html>