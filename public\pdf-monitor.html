<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Monitor - ForeverBook Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .status-card {
            margin-bottom: 20px;
        }

        .pdf-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
        }

        .storage-badge {
            font-size: 0.8em;
        }

        .download-btn {
            margin-top: 10px;
        }

        .refresh-btn {
            margin-bottom: 20px;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1>PDF Monitor</h1>
                <p class="text-muted">Monitor PDF storage and download functionality</p>
            </div>
            <div>
                <a href="/print-monitor" class="btn btn-outline-primary">Print Order Monitor</a>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card status-card">
                    <div class="card-header">
                        <h5>Memory Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="memoryStatus">Loading...</div>
                        <button class="btn btn-primary refresh-btn" onclick="loadMemoryStatus()">Refresh</button>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card status-card">
                    <div class="card-header">
                        <h5>Available PDFs</h5>
                    </div>
                    <div class="card-body">
                        <div id="availablePDFs">Loading...</div>
                        <button class="btn btn-success refresh-btn" onclick="loadAvailablePDFs()">Refresh</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>All Books</h5>
                    </div>
                    <div class="card-body">
                        <div id="allBooks">Loading...</div>
                        <button class="btn btn-info refresh-btn" onclick="loadAllBooks()">Refresh</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test PDF Download</h5>
                    </div>
                    <div class="card-body">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="testBookId"
                                placeholder="Enter Book ID to test download">
                            <button class="btn btn-warning" onclick="testPDFDownload()">Test Download</button>
                        </div>
                        <div id="testResult"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function loadMemoryStatus() {
            try {
                const response = await fetch('/api/memory-status');
                const data = await response.json();

                const html = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Memory Usage:</h6>
                            <ul class="list-unstyled">
                                <li>RSS: ${data.memoryUsage.rss}</li>
                                <li>Heap Used: ${data.memoryUsage.heapUsed}</li>
                                <li>Heap Total: ${data.memoryUsage.heapTotal}</li>
                                <li>External: ${data.memoryUsage.external}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>PDF Storage:</h6>
                            <ul class="list-unstyled">
                                <li>PDFs Stored: ${data.tempStorage.pdfsStored}/${data.tempStorage.maxAllowed}</li>
                                <li>Total Size: ${Math.round(data.tempStorage.totalSize / 1024)} KB</li>
                                <li>Average Size: ${data.tempStorage.averageSize}</li>
                            </ul>
                        </div>
                    </div>
                    <p><strong>Uptime:</strong> ${data.uptime}</p>
                `;

                document.getElementById('memoryStatus').innerHTML = html;
            } catch (error) {
                document.getElementById('memoryStatus').innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
            }
        }

        async function loadAvailablePDFs() {
            try {
                const response = await fetch('/api/available-pdfs');
                const data = await response.json();

                if (data.total === 0) {
                    document.getElementById('availablePDFs').innerHTML = '<div class="alert alert-info">No PDFs currently in memory</div>';
                    return;
                }

                const html = data.pdfs.map(pdf => `
                    <div class="pdf-item">
                        <div class="row">
                            <div class="col-md-8">
                                <strong>ID:</strong> ${pdf.id}<br>
                                <strong>Filename:</strong> ${pdf.filename}<br>
                                <strong>Size:</strong> ${pdf.size}<br>
                                <strong>Age:</strong> ${pdf.age}<br>
                                <strong>Expires:</strong> ${new Date(pdf.expiresAt).toLocaleString()}
                            </div>
                            <div class="col-md-4 text-end">
                                <a href="${pdf.downloadUrl}" class="btn btn-success btn-sm download-btn" target="_blank">Download</a>
                                <a href="/print-book.html?id=${pdf.id}" class="btn btn-primary btn-sm download-btn" target="_blank">Print</a>
                            </div>
                        </div>
                    </div>
                `).join('');

                document.getElementById('availablePDFs').innerHTML = html;
            } catch (error) {
                document.getElementById('availablePDFs').innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
            }
        }

        async function loadAllBooks() {
            try {
                const response = await fetch('/api/books');
                const data = await response.json();

                if (data.total === 0) {
                    document.getElementById('allBooks').innerHTML = '<div class="alert alert-info">No books found</div>';
                    return;
                }

                const html = `
                    <div class="mb-3">
                        <span class="badge bg-primary">Total: ${data.total}</span>
                        <span class="badge bg-success">Memory: ${data.memoryCount}</span>
                        <span class="badge bg-info">Filesystem: ${data.filesystemCount}</span>
                    </div>
                    ${data.books.map(book => `
                        <div class="pdf-item">
                            <div class="row">
                                <div class="col-md-8">
                                    <strong>ID:</strong> ${book.id}<br>
                                    <strong>Title:</strong> ${book.title}<br>
                                    <strong>Storage:</strong> <span class="badge ${book.storage === 'memory' ? 'bg-success' : 'bg-info'} storage-badge">${book.storage}</span><br>
                                    ${book.size ? `<strong>Size:</strong> ${book.size}<br>` : ''}
                                    ${book.age ? `<strong>Age:</strong> ${book.age}<br>` : ''}
                                    <strong>Generated:</strong> ${new Date(book.generatedAt).toLocaleString()}
                                </div>
                                <div class="col-md-4 text-end">
                                    <a href="${book.downloadUrl}" class="btn btn-success btn-sm download-btn" target="_blank">Download</a>
                                    <a href="/print-book.html?id=${book.id}" class="btn btn-primary btn-sm download-btn" target="_blank">Print</a>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                `;

                document.getElementById('allBooks').innerHTML = html;
            } catch (error) {
                document.getElementById('allBooks').innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
            }
        }

        async function testPDFDownload() {
            const bookId = document.getElementById('testBookId').value.trim();
            if (!bookId) {
                document.getElementById('testResult').innerHTML = '<div class="alert alert-warning">Please enter a Book ID</div>';
                return;
            }

            try {
                // First check if PDF exists
                const statusResponse = await fetch(`/api/pdf-status/${bookId}`);
                const statusData = await statusResponse.json();

                if (statusData.available) {
                    const result = `
                        <div class="alert alert-success">
                            <strong>PDF Found!</strong><br>
                            Storage: ${statusData.storage}<br>
                            Size: ${statusData.size ? Math.round(statusData.size / 1024) + ' KB' : 'Unknown'}<br>
                            <a href="/download/${bookId}" class="btn btn-success btn-sm mt-2" target="_blank">Download PDF</a>
                        </div>
                    `;
                    document.getElementById('testResult').innerHTML = result;
                } else {
                    const result = `
                        <div class="alert alert-warning">
                            <strong>PDF Not Found</strong><br>
                            ${statusData.message}<br>
                            ${statusData.availableBooks ? `<br><strong>Available Books:</strong> ${statusData.availableBooks.map(b => b.id).join(', ')}` : ''}
                        </div>
                    `;
                    document.getElementById('testResult').innerHTML = result;
                }
            } catch (error) {
                document.getElementById('testResult').innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
            }
        }

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function () {
            loadMemoryStatus();
            loadAvailablePDFs();
            loadAllBooks();
        });
    </script>
</body>

</html>