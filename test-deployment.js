const axios = require('axios');

// Configuration
const BASE_URL = process.env.HEROKU_APP_URL || 'http://localhost:3000';

async function testDeployment() {
    console.log('Testing deployment at:', BASE_URL);

    try {
        // Test 1: Check if server is running
        console.log('\n1. Testing server health...');
        const healthResponse = await axios.get(`${BASE_URL}/`);
        console.log('✅ Server is running');

        // Test 2: Test Alexanders print API endpoint (without actual submission)
        console.log('\n2. Testing Alexanders print API endpoint...');
        try {
            const testOrder = {
                orderKey1: `test-${Date.now()}`,
                photobookItems: [
                    {
                        coverUrl: "https://example.com/cover.pdf",
                        gutsUrl: "https://example.com/content.pdf",
                        sku: "6x9-hardcover-book",
                        quantity: 1
                    }
                ],
                shipping: {
                    shipMethod: "UPSGROUND",
                    address: {
                        name: "Test User",
                        address1: "123 Test St",
                        city: "Test City",
                        state: "TS",
                        postalCode: "12345",
                        countryCode: "US",
                        phoneNumber: "1234567890"
                    }
                }
            };

            const printResponse = await axios.post(`${BASE_URL}/api/print-order`, testOrder);
            console.log('✅ Print API endpoint is accessible');
        } catch (error) {
            if (error.response?.status === 500 && error.response?.data?.details) {
                console.log('✅ Print API endpoint is accessible (expected error due to missing API key)');
            } else {
                console.log('❌ Print API endpoint test failed:', error.message);
            }
        }

        // Test 3: Test environment variables
        console.log('\n3. Testing environment variables...');
        const envVars = [
            'OPENAI_API_KEY',
            'REPLICATE_API_TOKEN',
            'ALEXANDERS_API_KEY',
            'ALEXANDERS_API_URL'
        ];

        for (const envVar of envVars) {
            if (process.env[envVar]) {
                console.log(`✅ ${envVar} is set`);
            } else {
                console.log(`⚠️  ${envVar} is not set (optional for testing)`);
            }
        }

        console.log('\n🎉 Deployment test completed successfully!');
        console.log('\nNext steps:');
        console.log('1. Set up your API keys in Heroku:');
        console.log('   heroku config:set OPENAI_API_KEY=your_key');
        console.log('   heroku config:set REPLICATE_API_TOKEN=your_token');
        console.log('   heroku config:set ALEXANDERS_API_KEY=your_key');
        console.log('2. Test book generation with real API keys');
        console.log('3. Test print order submission');

    } catch (error) {
        console.error('❌ Deployment test failed:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 Make sure the server is running on the correct port');
        }
    }
}

// Run the test
testDeployment(); 