# Print Order Monitoring System

## Overview

The ForeverBook application now includes a comprehensive print order monitoring system that tracks orders from submission through completion. This system provides real-time visibility into the status of your print orders and integrates seamlessly with the Alexanders Print API.

## Features

### 🎯 **Order Tracking**
- Track all print orders with unique order keys
- Store order metadata including book ID, SKU, quantity, and shipping info
- Maintain order history across server restarts

### 📊 **Real-time Status Updates**
- Automatic status updates via webhooks from Alexanders API
- Track order progression: pending → processing → printing → shipped
- Handle error states and cancellations

### 🚚 **Shipment Tracking**
- Record shipping information including carrier and tracking numbers
- Track shipping costs and delivery dates
- Monitor multiple shipments per order

### ⚠️ **Error Handling**
- Capture and display processing errors
- Track errors by item key for granular debugging
- Maintain error history for troubleshooting

### 🎛️ **Order Management**
- View detailed order information
- Refresh order status from Alexanders API
- Cancel orders when needed
- Export order data for reporting

## Quick Start

### 1. Access the Monitor
Visit `/print-monitor` on your deployed application to access the monitoring dashboard.

### 2. Submit an Order
Orders are automatically tracked when submitted via the PDF monitor or API:
```bash
curl -X POST https://your-app.herokuapp.com/api/print-order \
  -H "Content-Type: application/json" \
  -d '{
    "orderKey1": "my-order-123",
    "bookId": "book-456",
    "coverUrl": "https://example.com/cover.pdf",
    "gutsUrl": "https://example.com/content.pdf",
    "sku": "6x9-hardcover-book",
    "quantity": 1
  }'
```

### 3. Monitor Progress
The dashboard will automatically show:
- Order status and timeline
- Status updates from Alexanders
- Shipping information when available
- Any errors that occur

## Dashboard Tabs

### 📈 **Overview Tab**
- **Statistics Cards**: Total orders, pending, processing, shipped
- **Recent Orders**: Latest 5 orders with quick actions
- **Auto-refresh**: Updates every 30 seconds

### 📋 **All Orders Tab**
- **Complete Order List**: All orders with full details
- **Status Badges**: Color-coded status indicators
- **Action Buttons**: View details, refresh status, cancel order
- **Sorting**: Orders sorted by creation date (newest first)

### 🔍 **Order Details Tab**
- **Order Information**: Complete order metadata
- **Status Timeline**: All status updates with timestamps
- **Shipment Details**: Carrier, tracking, costs, dates
- **Error Log**: Any errors with messages and timestamps
- **Management Actions**: Refresh and cancel options

## API Endpoints

### Order Management
```bash
# Get all orders
GET /api/orders

# Get specific order with details
GET /api/orders/:orderKey

# Submit new order
POST /api/print-order

# Get order status (local + remote)
GET /api/print-status/:orderKey

# Cancel order
DELETE /api/print-order/:orderKey
```

### Webhook Endpoints (for Alexanders API)
```bash
# Handle printing status updates
PUT /printing/:orderKey1

# Handle shipment notifications
PUT /shipped/:orderKey1

# Handle error notifications
PUT /error/:orderKey1
```

## Data Storage

### File Structure
```
/data/
├── orders.json          # Order records
├── status_updates.json  # Status update history
├── shipments.json       # Shipping information
└── errors.json         # Error records
```

### Data Persistence
- **Cross-platform**: Works on Windows, Mac, Linux, and Heroku
- **Automatic**: Files created when needed
- **Persistent**: Data survives server restarts
- **JSON Format**: Human-readable and easily exportable

## Webhook Integration

### Setting Up Webhooks
1. **Configure Alexanders API**: Provide your webhook URLs to Alexanders
2. **Verify API Key**: Ensure your `ALEXANDERS_API_KEY` is set
3. **Test Endpoints**: Use the test commands below

### Webhook URLs
```
https://your-app.herokuapp.com/printing/{orderKey1}
https://your-app.herokuapp.com/shipped/{orderKey1}
https://your-app.herokuapp.com/error/{orderKey1}
```

### Testing Webhooks
```bash
# Test printing status update
curl -X PUT https://your-app.herokuapp.com/printing/test-order-123 \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: your_api_key" \
  -d '{"dueDate": "2024-01-15T10:00:00Z"}'

# Test shipment notification
curl -X PUT https://your-app.herokuapp.com/shipped/test-order-123 \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: your_api_key" \
  -d '{
    "shipMethod": "UPS Ground",
    "carrier": "UPS",
    "trackingNumber": "1Z999AA1234567890",
    "dateShipped": "2024-01-16T14:30:00Z",
    "cost": 1250
  }'

# Test error notification
curl -X PUT https://your-app.herokuapp.com/error/test-order-123 \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: your_api_key" \
  -d '{
    "itemKey": "item-1",
    "message": "Failed to download cover image"
  }'
```

## Status Types

### Order Statuses
- **pending**: Order submitted, waiting for processing
- **processing**: Order being prepared for printing
- **printing**: Order currently being printed
- **scheduled**: Order scheduled with due date
- **shipped**: Order shipped with tracking
- **error**: Order encountered an error
- **cancelled**: Order was cancelled

### Update Types
- **printing**: Printing status updates
- **shipped**: Shipment notifications
- **error**: Error notifications

## Troubleshooting

### Common Issues

1. **Orders not appearing**
   - Check that orders are being submitted correctly
   - Verify the `/data` directory is writable
   - Check server logs for errors

2. **Webhooks not working**
   - Verify API key is correct
   - Check webhook URLs are accessible
   - Test with curl commands above

3. **Status not updating**
   - Check Alexanders API is sending webhooks
   - Verify webhook endpoints are responding
   - Use "Refresh Status" button to manually update

4. **Data persistence issues**
   - Ensure `/data` directory exists and is writable
   - Check file permissions
   - Verify JSON files are not corrupted

### Debug Commands
```bash
# Check order data
curl https://your-app.herokuapp.com/api/orders

# Check specific order
curl https://your-app.herokuapp.com/api/orders/your-order-key

# Check server logs
heroku logs --tail
```

## Best Practices

### For Development
- Test webhooks locally using ngrok
- Use unique order keys for testing
- Monitor the dashboard during development
- Check logs for webhook errors

### For Production
- Set up proper API keys and webhook URLs
- Monitor order completion rates
- Set up alerts for error conditions
- Regular backup of `/data` directory

### For Monitoring
- Check dashboard regularly for stuck orders
- Monitor error rates and types
- Track shipping performance
- Use order details for customer support

## Integration with Existing Workflow

### From PDF Monitor
1. Generate a book via the main application
2. Go to `/pdf-monitor` to see available PDFs
3. Click "Print" button to submit order
4. Order automatically appears in print monitor
5. Track progress via `/print-monitor`

### From API
1. Submit order via `/api/print-order`
2. Order immediately tracked in system
3. Monitor via API endpoints or dashboard
4. Receive webhook updates automatically

## Future Enhancements

- **Email Notifications**: Alert customers of status changes
- **Export Functionality**: Download order data as CSV/Excel
- **Advanced Filtering**: Filter orders by status, date, etc.
- **Bulk Operations**: Cancel or refresh multiple orders
- **Analytics Dashboard**: Order completion rates, error analysis
- **API Rate Limiting**: Protect against abuse
- **Data Archiving**: Move old orders to archive storage

---

For technical support or questions about the print monitoring system, please refer to the main application documentation or contact the development team. 