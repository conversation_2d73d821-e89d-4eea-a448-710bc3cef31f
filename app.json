{"name": "foreverbook-cbgen", "description": "AI Children's Book Generator with Print Integration", "repository": "https://github.com/yourusername/foreverbook-cbgen", "logo": "https://node-js-sample.herokuapp.com/node.png", "keywords": ["node", "express", "ai", "children", "book", "generator"], "env": {"OPENAI_API_KEY": {"description": "Your OpenAI API key", "required": true}, "REPLICATE_API_TOKEN": {"description": "Your Replicate API token", "required": true}, "ALEXANDERS_API_KEY": {"description": "Your Alexanders Print API key", "required": false}, "ALEXANDERS_API_URL": {"description": "Alexanders API URL (dev/prod)", "value": "https://devapi.divvy.systems", "required": false}}, "formation": {"web": {"quantity": 1, "size": "basic"}}, "buildpacks": [{"url": "hero<PERSON>/nodejs"}]}