# foreverbook-cbgen

Foreverbook Children's Book Generator with Print Integration

## Project Structure

This project is now split into two parts:
1. Book Generation Service (Current Node.js backend)
2. Next.js Frontend (New) - For Vercel hosting and print integration

## Quick Deploy to Heroku

For testing purposes, you can quickly deploy this to Heroku:

```bash
# Install Heroku CLI and login
heroku login

# Create a new Heroku app
heroku create your-app-name

# Set environment variables
heroku config:set OPENAI_API_KEY=your_openai_api_key
heroku config:set REPLICATE_API_TOKEN=your_replicate_token
heroku config:set ALEXANDERS_API_KEY=your_alexanders_api_key

# Deploy
git add .
git commit -m "Deploy to Heroku"
git push heroku main

# Open the app
heroku open
```

For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

## Book Generation Service Setup

Setup node environment, preferably using nvm.

```bash
nvm install
nvm use
```

Now install the dependencies:

```bash
npm install
```

Then run the project

```bash
npm run start
```
This will start a local server on port 3000. You can access the app at `http://localhost:3000`.

### Environment Variables

You can set the environment variables in a `.env` file. The following variables are required:

- `OPENAI_API_KEY`: Your OpenAI API key.
- `REPLICATE_API_TOKEN`: The token used for Replicate.
- `ALEXANDERS_API_KEY`: Your Alexanders Print API key.
- `ALEXANDERS_API_URL`: Alexanders API URL (dev/prod).

### Image Services

- replicate: This is the a multi hosted image generation service. It uses the Replicate API to generate images.
- dalle: This is an alternative image generation service. It uses the DALL-E API to generate images.

## Print Integration

The project integrates with Alexanders Print API for physical book production. The integration includes:

- Order submission endpoint
- Status update webhooks
- Print progress tracking
- Shipping status updates

### Print API Integration

The print integration uses the Alexanders API (https://devapi.divvy.systems) with the following features:

- Book order submission
- Order status tracking
- Shipping updates
- Error handling and retries

### Development Environment

For development and testing:
- Use https://devapi.divvy.systems
- Test orders in sandbox environment
- Monitor orders in http://devpartners.divvy.systems

### Production Environment

For production:
- Use https://api.divvy.systems
- Monitor orders in http://partners.divvy.systems
- Requires production API key activation

### Error Handling

Error generating book: Error: Prediction failed: The output was flagged as sensitive. Please try again with different inputs. (E005)

This is a known issue with the Replicate API. It happens when the prompt is too similar to the training data.

To fix this, we can try to generate the book with a different prompt.

## Next.js Frontend

The new Next.js frontend provides:
- Modern UI for book creation
- Print order submission interface
- Order status tracking
- Debug-friendly API response display
- Progress indicators for print orders

To run the Next.js frontend:

```bash
cd frontend
npm install
npm run dev
```

The frontend will be available at `http://localhost:3001`.

## API Endpoints

### Book Generation
- `POST /generate-book`: Generate a new children's book

### Print Integration (Alexanders API)
- `POST /api/print-order`: Submit a photobook order
- `GET /api/print-status/:orderKey`: Get order status
- `POST /api/print-webhook`: Handle status updates
- `DELETE /api/print-order/:orderKey`: Cancel an order

### Legacy Print (Divvy API)
- `POST /api/print`: Submit a standard print job

## Testing

Run the deployment test script:

```bash
node test-deployment.js
```

This will test all endpoints and verify your deployment is working correctly.

## Features

- **AI-Powered Content Generation**: Uses OpenAI GPT-4 to create personalized stories
- **Multiple Image Generation Services**: Support for DALL-E, Midjourney, Pruna, Recraft, and GPT Image 1
- **Automatic PDF Generation**: Creates beautiful, print-ready PDFs
- **Async Processing**: Handles long-running book generation without timeouts
- **Memory-Based PDF Storage**: Optimized for Heroku deployment with automatic cleanup
- **Real-time Progress Tracking**: Monitor book generation progress
- **Print Integration**: Direct integration with printing services
- **PDF Monitoring Dashboard**: Monitor PDF storage and download functionality

## PDF Download System

The application uses a memory-based storage system optimized for Heroku deployment:

- **Memory Storage**: PDFs are stored in application memory for up to 30 minutes
- **Public URLs**: PDFs are accessible via `/download/:bookId` URLs
- **Automatic Cleanup**: Old PDFs are automatically removed to prevent memory issues
- **Monitoring**: Use `/pdf-monitor` to check PDF availability and memory usage

### Quick Test
Visit `/pdf-monitor` on your deployed app to monitor PDF storage and test downloads.

## Getting Started
