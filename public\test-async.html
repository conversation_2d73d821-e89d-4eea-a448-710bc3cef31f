<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Async Book Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input,
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }

        .status.processing {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .status.completed {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
            width: 0%;
        }

        .download-link {
            display: inline-block;
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 10px;
            margin-right: 10px;
        }

        .print-link {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 10px;
        }

        .monitor-link {
            display: inline-block;
            background-color: #6c757d;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 10px;
            margin-left: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Test Async Book Generation</h1>
        <p>This page tests the new async book generation that avoids Heroku timeouts.</p>

        <form id="bookForm">
            <div class="form-group">
                <label for="childName">Child's Name:</label>
                <input type="text" id="childName" name="childName" value="Test Child" required>
            </div>

            <div class="form-group">
                <label for="childAge">Child's Age:</label>
                <input type="number" id="childAge" name="childAge" value="5" min="1" max="12" required>
            </div>

            <div class="form-group">
                <label for="childGender">Gender:</label>
                <select id="childGender" name="childGender" required>
                    <option value="boy">Boy</option>
                    <option value="girl">Girl</option>
                </select>
            </div>

            <div class="form-group">
                <label for="childInterests">Interests:</label>
                <input type="text" id="childInterests" name="childInterests" value="dinosaurs" required>
            </div>

            <div class="form-group">
                <label for="childHairColor">Hair Color:</label>
                <input type="text" id="childHairColor" name="childHairColor" value="brown" required>
            </div>

            <div class="form-group">
                <label for="childHairStyle">Hair Style:</label>
                <input type="text" id="childHairStyle" name="childHairStyle" value="short" required>
            </div>

            <div class="form-group">
                <label for="childEyeColor">Eye Color:</label>
                <input type="text" id="childEyeColor" name="childEyeColor" value="blue" required>
            </div>

            <div class="form-group">
                <label for="childSkinTone">Skin Tone:</label>
                <input type="text" id="childSkinTone" name="childSkinTone" value="fair" required>
            </div>

            <div class="form-group">
                <label for="imageService">Image Service:</label>
                <select id="imageService" name="imageService">
                    <option value="dalle">DALL-E</option>
                    <option value="gpt-image-1">GPT Image 1</option>
                    <option value="midjourney">Midjourney</option>
                    <option value="pruna">Pruna</option>
                    <option value="recraft">Recraft</option>
                </select>
            </div>

            <button type="submit" id="generateBtn">Generate Book</button>
        </form>

        <div id="status" class="status">
            <div id="statusMessage"></div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="downloadSection" style="display: none;">
                <a id="downloadLink" class="download-link" href="#" target="_blank" style="display:none;">Download
                    Book</a>
                <a id="printLink" class="print-link" href="#" target="_blank" style="display:none;">Send to Printer</a>
                <a id="monitorLink" class="monitor-link" href="/pdf-monitor" target="_blank" style="display:none;">View
                    in Monitor</a>
            </div>
        </div>
    </div>

    <script>
        let currentJobId = null;
        let statusInterval = null;
        let currentBookId = null;

        document.getElementById('bookForm').addEventListener('submit', async function (e) {
            e.preventDefault();

            const generateBtn = document.getElementById('generateBtn');
            const status = document.getElementById('status');
            const statusMessage = document.getElementById('statusMessage');

            generateBtn.disabled = true;
            status.style.display = 'block';
            status.className = 'status processing';
            statusMessage.textContent = 'Starting book generation...';

            // Get form data
            const formData = new FormData(this);
            const bookData = Object.fromEntries(formData.entries());

            try {
                // Start book generation
                const response = await fetch('/generate-book', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(bookData)
                });

                const result = await response.json();

                if (result.jobId) {
                    currentJobId = result.jobId;
                    statusMessage.textContent = result.message;
                    currentBookId = result.bookId;
                    showDownloadAndPrintLinks(result.bookId);
                    document.getElementById('downloadSection').style.display = 'block';

                    // Start polling for status
                    startStatusPolling();
                } else {
                    throw new Error(result.error || 'Failed to start book generation');
                }

            } catch (error) {
                status.className = 'status error';
                statusMessage.textContent = `Error: ${error.message}`;
                generateBtn.disabled = false;
            }
        });

        function startStatusPolling() {
            if (statusInterval) {
                clearInterval(statusInterval);
            }

            statusInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/api/book-status/${currentJobId}`);
                    const statusData = await response.json();

                    const status = document.getElementById('status');
                    const statusMessage = document.getElementById('statusMessage');
                    const progressFill = document.getElementById('progressFill');
                    const downloadSection = document.getElementById('downloadSection');
                    const downloadLink = document.getElementById('downloadLink');
                    const printLink = document.getElementById('printLink');
                    const generateBtn = document.getElementById('generateBtn');

                    statusMessage.textContent = statusData.message;
                    progressFill.style.width = `${statusData.progress}%`;

                    if (statusData.status === 'completed') {
                        status.className = 'status completed';
                        downloadSection.style.display = 'block';
                        showDownloadAndPrintLinks(currentBookId);
                        generateBtn.disabled = false;
                        clearInterval(statusInterval);
                    } else if (statusData.status === 'error') {
                        status.className = 'status error';
                        generateBtn.disabled = false;
                        clearInterval(statusInterval);
                    }

                } catch (error) {
                    console.error('Error polling status:', error);
                }
            }, 2000); // Poll every 2 seconds
        }

        function showDownloadAndPrintLinks(bookId) {
            const downloadLink = document.getElementById('downloadLink');
            const printLink = document.getElementById('printLink');
            const monitorLink = document.getElementById('monitorLink');
            if (bookId) {
                downloadLink.href = `/download/${bookId}.pdf`;
                printLink.href = `/print-book.html?id=${bookId}`;
                downloadLink.style.display = '';
                printLink.style.display = '';
                monitorLink.style.display = '';
                downloadLink.classList.remove('disabled');
                printLink.classList.remove('disabled');
                monitorLink.classList.remove('disabled');
            } else {
                downloadLink.href = '#';
                printLink.href = '#';
                downloadLink.style.display = 'none';
                printLink.style.display = 'none';
                monitorLink.style.display = 'none';
                downloadLink.classList.add('disabled');
                printLink.classList.add('disabled');
                monitorLink.classList.add('disabled');
            }
        }

        // On page load, hide the links
        showDownloadAndPrintLinks(null);
    </script>
</body>

</html>