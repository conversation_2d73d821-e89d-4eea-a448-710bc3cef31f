{"openapi": "3.0.1", "info": {"title": "Divvy Standard API", "version": "v1.1.28"}, "paths": {"/v1.1/order": {"post": {"tags": ["Order"], "summary": "Places new order for fulfillment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseModel"}}}}, "200": {"description": "Success"}}}}, "/v1.1/order/{orderKey1}": {"delete": {"tags": ["Order"], "summary": "Cancel Order - This call will cancel an order if can be. If the order has already started production, it will not ship but you will be billed for the items.", "parameters": [{"name": "orderKey1", "in": "path", "description": "key sent in original request", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseModel"}}}}, "200": {"description": "Success"}}}}, "/v1.1/order/cancelifnotprinting/{orderKey1}": {"delete": {"tags": ["Order"], "summary": "Cancel Order - This call will only cancel the order if the order has not yet entered the production process.", "parameters": [{"name": "orderKey1", "in": "path", "description": "key sent in original request", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseModel"}}}}, "200": {"description": "Success"}}}}, "/v1.1/order/shippingaddress/{orderKey1}": {"put": {"tags": ["Order"], "summary": "Change Shipping Address", "parameters": [{"name": "orderKey1", "in": "path", "description": "key sent in original request", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShippingAddressModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseModel"}}}}, "200": {"description": "Success"}}}}, "/v1.1/order/shippingmethod/{orderKey1}": {"put": {"tags": ["Order"], "summary": "Change Shipping method", "parameters": [{"name": "orderKey1", "in": "path", "description": "key sent in original request", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShippingMethodModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseModel"}}}}, "200": {"description": "Success"}}}}}, "components": {"schemas": {"ErrorResponseModel": {"required": ["message"], "type": "object", "properties": {"message": {"type": "string"}, "modelErrors": {"type": "array", "items": {"$ref": "#/components/schemas/ModelError"}, "nullable": true}}, "additionalProperties": false}, "InventoryItemModel": {"required": ["quantity", "sku"], "type": "object", "properties": {"itemKey": {"maxLength": 50, "minLength": 0, "type": "string", "description": "(Optional) Unique, for this order, identifier indicating the item.", "nullable": true}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "description": "(Optional) description for the item.", "nullable": true}, "sku": {"minLength": 1, "type": "string", "description": "(Required) Product identifier"}, "quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "description": "(Required) Quantity for the line item", "format": "int32"}, "bundleKey": {"maxLength": 300, "minLength": 0, "type": "string", "description": "Bundle identifier key", "nullable": true}}, "additionalProperties": false}, "ModelError": {"type": "object", "properties": {"property": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "NotebookItemModel": {"required": ["coverUrl", "quantity", "sku"], "type": "object", "properties": {"itemKey": {"maxLength": 50, "minLength": 0, "type": "string", "description": "(Optional) Unique, for this order, identifier indicating the item.", "nullable": true}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "description": "(Optional) description for the item.", "nullable": true}, "sku": {"minLength": 1, "type": "string", "description": "(Required) Product identifier"}, "quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "description": "(Required) Quantity for the line item", "format": "int32"}, "bundleKey": {"maxLength": 300, "minLength": 0, "type": "string", "description": "Bundle identifier key", "nullable": true}, "foilUrl": {"type": "string", "description": "(Optional) Artwork file url for foil.", "nullable": true}, "foilColor": {"enum": ["GOLD", "ROSEGOLD", "SILVER", "MATTEGOLD", "SPOTUV", "COPPER", "IRIDESCENT"], "type": "string", "description": "(Optional) Pdf cover foil color. Must be specified if foilUrl is specified.", "nullable": true}, "coverUrl": {"minLength": 1, "type": "string", "description": "(Required) Pdf cover artwork file url."}, "pageStyle": {"type": "string", "description": "(Required, if product allows different styles) Guts page style. Possible values:\r\nWH<PERSON><PERSON>,\r\n<PERSON><PERSON><PERSON>,\r\n<PERSON><PERSON><PERSON><PERSON>,\r\nLINED,\r\nKRAFT_LINED,\r\nKRAFT_DOT_GRID,\r\nWHITE_LINED,\r\nWHITE_DOT_GRID,\r\nMIXED_MEDIA,\r\nGRAPH", "nullable": true}}, "additionalProperties": false}, "OrderModel": {"required": ["orderKey1"], "type": "object", "properties": {"orderKey1": {"maxLength": 500, "minLength": 0, "type": "string", "description": "(Required) Unique identifier for the order"}, "orderKey2": {"maxLength": 50, "minLength": 0, "type": "string", "description": "(Optional) aditional identifier for the order", "nullable": true}, "standardPrintItems": {"type": "array", "items": {"$ref": "#/components/schemas/StandardPrintItemModel"}, "description": "Array of standard print items (cards and invites).\r\nAt least one of standardPrintItems -or- photobookItems -or- notebookItems -or- inventoryItems needs to be present.", "nullable": true}, "photobookItems": {"type": "array", "items": {"$ref": "#/components/schemas/PhotobookItemModel"}, "description": "Array of items of type photobook.\r\nAt least one of standardPrintItems -or- photobookItems -or- notebookItems -or- inventoryItems needs to be present.", "nullable": true}, "notebookItems": {"type": "array", "items": {"$ref": "#/components/schemas/NotebookItemModel"}, "description": "Array of items of type notebook.\r\nAt least one of standardPrintItems -or- photobookItems -or- notebookItems -or- inventoryItems needs to be present.", "nullable": true}, "inventoryItems": {"type": "array", "items": {"$ref": "#/components/schemas/InventoryItemModel"}, "description": "Array of inventory items.\r\nAt least one of standardPrintItems -or- photobookItems -or- notebookItems -or- inventoryItems needs to be present.", "nullable": true}, "shipping": {"$ref": "#/components/schemas/ShippingModel"}, "returnAddress": {"$ref": "#/components/schemas/ReturnAddressModel"}, "rushOrder": {"type": "boolean", "description": "(Optional) boolean to indicate rush. Default is false.", "nullable": true}, "nonBillableReprint": {"type": "boolean", "description": "Flag to make an order non-billable", "nullable": true}}, "additionalProperties": false}, "PhotobookItemModel": {"required": ["gutsUrl", "quantity", "sku"], "type": "object", "properties": {"itemKey": {"maxLength": 50, "minLength": 0, "type": "string", "description": "(Optional) Unique, for this order, identifier indicating the item.", "nullable": true}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "description": "(Optional) description for the item.", "nullable": true}, "sku": {"minLength": 1, "type": "string", "description": "(Required) Product identifier"}, "quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "description": "(Required) Quantity for the line item", "format": "int32"}, "bundleKey": {"maxLength": 300, "minLength": 0, "type": "string", "description": "Bundle identifier key", "nullable": true}, "foilUrl": {"type": "string", "description": "(Optional) Artwork file url for foil.", "nullable": true}, "foilColor": {"enum": ["GOLD", "ROSEGOLD", "SILVER", "MATTEGOLD", "SPOTUV", "COPPER", "IRIDESCENT"], "type": "string", "description": "(Optional) Pdf cover foil color. Must be specified if foilUrl is specified.", "nullable": true}, "coverUrl": {"type": "string", "description": "Pdf cover artwork file url.", "nullable": true}, "gutsUrl": {"minLength": 1, "type": "string", "description": "(Required) Pdf guts artwork file url."}}, "additionalProperties": false}, "ReturnAddressModel": {"required": ["address1", "city", "postalCode", "state"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 0, "type": "string", "description": "Only use for US return addresses and when specifically directed to by <PERSON><PERSON>, other don't include returnAddress object at all.", "nullable": true}, "company": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "address1": {"maxLength": 100, "minLength": 0, "type": "string", "description": "Only use for US return addresses and when specifically directed to by <PERSON><PERSON>, other don't include returnAddress object at all."}, "address2": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "city": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Only use for US return addresses and when specifically directed to by <PERSON><PERSON>, other don't include returnAddress object at all."}, "state": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Only use for US return addresses and when specifically directed to by <PERSON><PERSON>, other don't include returnAddress object at all."}, "postalCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Only use for US return addresses and when specifically directed to by <PERSON><PERSON>, other don't include returnAddress object at all."}, "phoneNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Phone number is required by the carrier when using an expedited shipping method.", "nullable": true}}, "additionalProperties": false}, "ShippingAddressModel": {"required": ["address1", "city", "countryCode", "name", "state"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 0, "type": "string"}, "company": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "address1": {"maxLength": 100, "minLength": 0, "type": "string"}, "address2": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "city": {"maxLength": 50, "minLength": 0, "type": "string"}, "state": {"maxLength": 50, "minLength": 0, "type": "string"}, "postalCode": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "countryCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Recommended to use ISO Alpha-3. Validation will be done against a country list"}, "phoneNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Phone number is required by the carrier when using an expedited shipping method.", "nullable": true}}, "additionalProperties": false}, "ShippingMethodModel": {"required": ["shipMethod"], "type": "object", "properties": {"shipMethod": {"type": "string", "description": "(Required) Shipping Method. Possible values:\r\nUPS1D: UPS Next Day Air\r\nUPS2D: UPS 2nd Day Air\r\nUPSGROUND: UPS Ground\r\nUPSMIEXPBPM: UPS MI Expedited BPM\r\nUSPSFIRSTCLASSLETTER: USPS First Class Mail (must be less than 1lb)\r\nUSPSPRIORITY: USPS Priority\r\nUSPSEXPRESSMAIL: USPS Priority Mail Express\r\nFEDEX1D: FedEx Standard Overnight\r\nFEDEX2D: FedEx 2-Day Air\r\nFEDEXINTLPRPAK: FedEx International Priority Pak\r\nFEDEXGROUND: FedEx Ground\r\nFEDEXONERATEPAK: FedEx One Rate Pak"}}, "additionalProperties": false}, "ShippingModel": {"required": ["address", "shipMethod"], "type": "object", "properties": {"shipMethod": {"type": "string", "description": "(Required) Shipping Method. Possible values:\r\nUPS1D: UPS Next Day Air\r\nUPS2D: UPS 2nd Day Air\r\nUPSGROUND: UPS Ground\r\nUPSMIEXPBPM: UPS MI Expedited BPM (max weight 15lbs)\r\nUSPSFIRSTCLASSLETTER: USPS First Class Mail (max weight 13oz)\r\nUSPSFIRSTCLASSPKG: USPS First Class Package (max weight 1lb)\r\nUSPSFIRSTCLASSPKGINTERNATIONAL: USPS First Class Package International (max weight 4lbs)\r\nUSPSPRIORITY: USPS Priority\r\nUSPSPRIORITYINTERNATIONAL: USPS Priority Mail International\r\nUSPSEXPRESSMAIL: USPS Priority Mail Express\r\nFEDEX1D: FedEx Standard Overnight\r\nFEDEX2D: FedEx 2-Day Air\r\nFEDEXINTLPRPAK: FedEx International Priority Pak\r\nFEDEXGROUND: FedEx Ground\r\nFEDEXONERATEPAK: FedEx One Rate Pak"}, "address": {"$ref": "#/components/schemas/ShippingAddressModel"}}, "additionalProperties": false}, "StandardPrintItemModel": {"required": ["duplex", "fileUrl", "quantity", "sku"], "type": "object", "properties": {"itemKey": {"maxLength": 50, "minLength": 0, "type": "string", "description": "(Optional) Unique, for this order, identifier indicating the item.", "nullable": true}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "description": "(Optional) description for the item.", "nullable": true}, "sku": {"minLength": 1, "type": "string", "description": "(Required) Product identifier"}, "quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "description": "(Required) Quantity for the line item", "format": "int32"}, "bundleKey": {"maxLength": 300, "minLength": 0, "type": "string", "description": "Bundle identifier key", "nullable": true}, "foilUrl": {"type": "string", "description": "(Optional) Artwork file url for foil.", "nullable": true}, "foilColor": {"enum": ["GOLD", "ROSEGOLD", "SILVER", "MATTEGOLD", "SPOTUV", "COPPER", "IRIDESCENT"], "type": "string", "description": "(Optional) Pdf cover foil color. Must be specified if foilUrl is specified.", "nullable": true}, "fileUrl": {"minLength": 1, "type": "string", "description": "(Required) Artwork file url."}, "mimeType": {"type": "string", "description": "(Optional) Artwork(s) file mime type. Default is application/pdf", "nullable": true}, "width": {"type": "number", "description": "(For some products) Finished width specified in inches", "format": "double", "nullable": true}, "height": {"type": "number", "description": "(For some products) Finished height specified in inches", "format": "double", "nullable": true}, "media": {"type": "string", "description": "(For some products) Media must be specified. Possible values:\r\n130#\r\n120# Sandshell\r\n2x2 Sticker\r\n16pt C1S", "nullable": true}, "folds": {"type": "string", "description": "(Optional) Field for specifying fold.\r\nValue must look like: v:3.5,7 -or- h:3.5,7\r\nwhere 'h' and 'v' refeers to horizontal and vertical respectively; and values are expressed in inches.", "nullable": true}, "variable": {"type": "boolean", "description": "(Optional) Field to specify that the item is a variable item. Default false.", "nullable": true}, "duplex": {"type": "boolean", "description": "(Required) Field to specify if an item should be printed duplex. True for two sided printing and False for single sided printing."}, "dieCut": {"type": "string", "description": "(For some products) DieCut must be specified. Ask Alexanders for possible values.", "nullable": true}, "backUrl": {"type": "string", "description": "(Optional) Back url file.", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"ApiKeyAuthHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Partner API key provided by <PERSON><PERSON>. Used in header to authorize.", "name": "X-API-KEY", "in": "header"}}}}