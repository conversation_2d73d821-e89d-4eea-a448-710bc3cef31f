const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');
const SVGtoPDF = require('svg-to-pdfkit');

const leftDedicationPath = path.join(__dirname, '../assets/pages/ForeverBook_Pages_Dedication-Left.png');
const rightDedicationPath = path.join(__dirname, '../assets/pages/ForeverBook_Pages_Dedication-Right.png');

// Add SVG support to PDFKit
PDFDocument.prototype.svg = function (svg, x, y, options) {
  return SVGtoPDF(this, svg, x, y, options);
};

// Helper function to validate image (exclude brand logos)
function isValidImage(imageBuffer) {
  try {
    // Check if image dimensions are reasonable (brand logos tend to be small)
    const img = PDFDocument.prototype.openImage(imageBuffer);
    const minDimension = Math.min(img.width, img.height);
    const maxDimension = Math.max(img.width, img.height);

    // Brand logos often have:
    // 1. Very small dimensions
    // 2. Square or near-square aspect ratios
    // 3. Transparent backgrounds
    const aspectRatio = maxDimension / minDimension;

    // Reject if image is too small (reduced from 200 to 50 pixels)
    if (minDimension < 50) {
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error validating image:', error);
    return false;
  }
}

const PAGE_BG_COLOR = '#FFFFFF';
const BLEED_SIZE = 9; // 0.125 inches = 9 points (72 points per inch)

// Create PDF document
async function createPDF(outputPath, bookContent, images, returnBuffer = false) {
  return new Promise(async (resolve, reject) => {
    try {
      // Define constants for the document
      const MARGIN = 72; // 1 inch margin
      const TEXT_BACKGROUND_OPACITY = 0.9;
      const LOGO_SIZE = 100; // Size for the logo on the back page

      let doc = new PDFDocument({
        size: [576 + (BLEED_SIZE * 2), 576 + (BLEED_SIZE * 2)], // Add bleed to both dimensions
        layout: 'portrait',
        margins: { // no document margins so page numbers can be pushed to the edges
          top: 0,
          bottom: 0,
          left: 0,
          right: 0
        }
      });

      // Register League Spartan font
      const fontPath = path.join(__dirname, '..', 'node_modules', '@fontsource', 'league-spartan', 'files', 'league-spartan-latin-400-normal.woff');
      doc.registerFont('LeagueSpartan', fontPath);

      let writeStream;
      if (returnBuffer) {
        // Collect chunks in memory using a proper writable stream
        const { Writable } = require('stream');
        const chunks = [];
        writeStream = new Writable({
          write(chunk, encoding, callback) {
            chunks.push(chunk);
            callback();
          }
        });
        writeStream.on('finish', () => {
          const buffer = Buffer.concat(chunks);
          resolve(buffer);
        });
      } else {
        // Write to file
        writeStream = fs.createWriteStream(outputPath);
        writeStream.on('finish', () => resolve());
      }

      doc.pipe(writeStream);

      // Define page dimensions including bleed
      const pageWidth = doc.page.width;
      const pageHeight = doc.page.height;
      const contentWidth = pageWidth - (BLEED_SIZE * 2);
      const contentHeight = pageHeight - (BLEED_SIZE * 2);

      // Helper function to add content with bleed
      const addContentWithBleed = (contentFunction) => {
        // Add bleed area background
        doc.rect(0, 0, pageWidth, pageHeight).fill(PAGE_BG_COLOR);
        // Add content with bleed offset
        contentFunction(BLEED_SIZE, BLEED_SIZE, contentWidth, contentHeight);
      };

      // 1. Title/cover page (image)
      if (images[0]) {
        try {
          const imageBuffer = Buffer.from(images[0], 'base64');
          if (isValidImage(imageBuffer)) {
            addContentWithBleed((x, y, width, height) => {
              const img = doc.openImage(imageBuffer);
              const imgAspectRatio = img.width / img.height;
              const pageAspectRatio = width / height;

              if (imgAspectRatio > pageAspectRatio) {
                const finalHeight = height;
                const finalWidth = height * imgAspectRatio;
                const xOffset = x + (width - finalWidth) / 2;
                doc.image(imageBuffer, xOffset, y, {
                  width: finalWidth,
                  height: finalHeight
                });
              } else {
                const finalWidth = width;
                const finalHeight = width / imgAspectRatio;
                const yOffset = y + (height - finalHeight) / 2;
                doc.image(imageBuffer, x, yOffset, {
                  width: finalWidth,
                  height: finalHeight
                });
              }
            });
          } else {
            addContentWithBleed((x, y, width, height) => {
              doc.rect(x, y, width, height).fill('#FFF8E7');
            });
          }
        } catch (error) {
          console.error('Error adding title page image:', error);
          addContentWithBleed((x, y, width, height) => {
            doc.rect(x, y, width, height).fill(PAGE_BG_COLOR);
          });
        }
      } else {
        addContentWithBleed((x, y, width, height) => {
          doc.rect(x, y, width, height).fill(PAGE_BG_COLOR);
        });
      }

      // 2. Add custom front endpaper page
      doc.addPage();
      addContentWithBleed((x, y, width, height) => {
        addFrontEndpaperPage(doc, bookContent.childName || 'Reader Name Here', x, y, width, height);
      });

      // 3. Add dedication pages after title page
      // Left dedication page
      try {
        doc.addPage();
        addContentWithBleed((x, y, width, height) => {
          doc.image(leftDedicationPath, x, y, {
            width: width,
            height: height
          });
        });
      } catch (error) {
        console.error('Error adding left dedication page:', error);
        doc.addPage();
        addContentWithBleed((x, y, width, height) => {
          doc.rect(x, y, width, height).fill(PAGE_BG_COLOR);
        });
      }

      // Right dedication page
      try {
        doc.addPage();
        addContentWithBleed((x, y, width, height) => {
          doc.image(rightDedicationPath, x, y, {
            width: width,
            height: height
          });
        });
      } catch (error) {
        console.error('Error adding right dedication page:', error);
        doc.addPage();
        addContentWithBleed((x, y, width, height) => {
          doc.rect(x, y, width, height).fill(PAGE_BG_COLOR);
        });
      }

      // Start story pages
      doc.addPage();

      // 4. Process story pages
      for (let i = 1; i < bookContent.pages.length; i++) {
        // Image page (left side)
        if (i > 1) {
          doc.addPage();
        }

        addContentWithBleed((x, y, width, height) => {
          doc.rect(x, y, width, height).fill(PAGE_BG_COLOR);

          if (images[i]) {
            try {
              const imageBuffer = Buffer.from(images[i], 'base64');
              if (isValidImage(imageBuffer)) {
                const img = doc.openImage(imageBuffer);
                const imgAspectRatio = img.width / img.height;
                const pageAspectRatio = width / height;

                if (imgAspectRatio > pageAspectRatio) {
                  const finalHeight = height;
                  const finalWidth = height * imgAspectRatio;
                  const xOffset = x + (width - finalWidth) / 2;
                  doc.image(imageBuffer, xOffset, y, {
                    width: finalWidth,
                    height: finalHeight
                  });
                } else {
                  const finalWidth = width;
                  const finalHeight = width / imgAspectRatio;
                  const yOffset = y + (height - finalHeight) / 2;
                  doc.image(imageBuffer, x, yOffset, {
                    width: finalWidth,
                    height: finalHeight
                  });
                }
              } else {
                console.log(`Skipping invalid image on page ${i} (likely a brand logo)`);
                doc.rect(x, y, width, height).fill('#FFF8E7');
              }
            } catch (error) {
              console.error(`Error adding image on page ${i}:`, error);
              doc.rect(x, y, width, height)
                .stroke()
                .fontSize(14)
                .fillColor('#000000')
                .text('Image could not be loaded', x, y + height / 2, {
                  width: width,
                  align: 'center'
                });
            }
          }
        });

        // Add page number at the bottom
        const leftPageNumber = i * 2 - 1;
        const dotSize = 18;
        doc
          .circle(pageWidth - MARGIN - BLEED_SIZE, pageHeight - MARGIN - BLEED_SIZE, dotSize)
          .opacity(0.5)
          .fill('#DDDDDD')

        doc
          .font('assets/fonts/Pangolin-Regular.ttf')
          .opacity(1)
          .fontSize(14)
          .fillColor('#000000')
          .text(`${leftPageNumber}`, pageWidth - MARGIN - BLEED_SIZE - (dotSize / 2), pageHeight - MARGIN - BLEED_SIZE, {
            width: dotSize,
            height: dotSize,
            align: 'center',
            baseline: 'middle',
            indent: 0,
          });

        // Text page (right side)
        doc.addPage();
        addContentWithBleed((x, y, width, height) => {
          doc.rect(x, y, width, height).fill(PAGE_BG_COLOR);

          // Add text with background
          doc.font('assets/fonts/Pangolin-Regular.ttf')
            .fontSize(16)
            .fillColor('#000000');

          // Add the story text
          const textOptions = {
            width: width - (MARGIN * 2.5),
            align: 'center',
            lineGap: 10
          };

          // Calculate text height for vertical centering
          const text = bookContent.pages[i].text;
          const textHeight = doc.heightOfString(text, textOptions);
          const textY = y + Math.max(MARGIN * 1.2, (height - textHeight) / 2);

          // Add the actual text
          doc
            .image('assets/images/page-border.png', x, y, {
              width: width,
              height: height
            })
            .fillColor('#000000')
            .text(text, x + MARGIN * 1.25, textY, textOptions);
        });

        // Add page number at the bottom
        const rightPageNumber = i * 2;
        doc
          .circle(pageWidth - MARGIN - BLEED_SIZE, pageHeight - MARGIN - BLEED_SIZE, dotSize)
          .opacity(0.5)
          .fill('#DDDDDD')

        doc
          .font('assets/fonts/Pangolin-Regular.ttf')
          .opacity(1)
          .fontSize(14)
          .fillColor('#000000')
          .text(`${rightPageNumber}`, pageWidth - MARGIN - BLEED_SIZE - (dotSize / 2), pageHeight - MARGIN - BLEED_SIZE, {
            width: dotSize,
            height: dotSize,
            align: 'center',
            baseline: 'middle',
          });
      }

      // Add QR code page as the final page
      doc.addPage();
      try {
        const backEndpaperPath = path.join(__dirname, '../assets/pages/ForeverBook_Pages_Back-Endpaper.png');
        addContentWithBleed((x, y, width, height) => {
          doc.image(backEndpaperPath, x, y, {
            width: width,
            height: height
          });
        });
      } catch (error) {
        console.error('Error adding QR code page:', error);
        addContentWithBleed((x, y, width, height) => {
          doc.rect(x, y, width, height).fill(PAGE_BG_COLOR);
        });
      }

      // Add quote and logo page
      doc.addPage();
      addContentWithBleed((x, y, width, height) => {
        doc.rect(x, y, width, height).fill(PAGE_BG_COLOR);

        try {
          const logoPath = path.join(__dirname, '..', 'assets', 'images', 'fb_logo_teal.svg');
          const svgContent = fs.readFileSync(logoPath, 'utf8');

          // Calculate position to center the logo horizontally and place it in lower 1/8
          const logoX = x + (width - LOGO_SIZE) / 2;
          const logoY = y + height * 0.875 - LOGO_SIZE / 2;

          // Add the SVG with explicit dimensions
          doc.svg(svgContent, logoX, logoY, {
            width: LOGO_SIZE,
            height: LOGO_SIZE
          });

          // Calculate the height of both text elements for vertical centering
          const quoteText = 'A story that speaks to a child\'s heart becomes a voice they carry forever.';
          const authorText = '- ForeverBook';

          doc.font('assets/fonts/Pangolin-Regular.ttf')
            .fontSize(16);

          const quoteHeight = doc.heightOfString(quoteText, { width: width - MARGIN * 2 });
          const authorHeight = doc.heightOfString(authorText, { width: width - MARGIN * 2 });
          const totalTextHeight = quoteHeight + authorHeight + doc.currentLineHeight();

          // Calculate vertical position to center the text in the page
          const textY = y + (height - totalTextHeight) / 2;

          // Add the quote with proper apostrophe
          doc.fillColor('#00A79D')
            .text(quoteText, x + MARGIN, textY, {
              width: width - MARGIN * 2,
              align: 'center'
            });

          // Add the attribution
          doc.text(authorText, x + MARGIN, doc.y + doc.currentLineHeight(), {
            width: width - MARGIN * 2,
            align: 'center'
          });
        } catch (error) {
          console.error('Error adding quote and logo to page:', error);
        }
      });

      // Finalize the PDF
      doc.end();

      writeStream.on('finish', () => {
        resolve();
      });

      writeStream.on('error', (error) => {
        reject(error);
      });

    } catch (error) {
      reject(error);
    }
  });
}

// Helper function for blank title page
function addDefaultTitlePage(doc, bookContent, x, y, width, height) {
  doc.rect(x, y, width, height).fill(PAGE_BG_COLOR);
}

// Helper to add the custom front endpaper page
function addFrontEndpaperPage(doc, childName, x, y, width, height) {
  // Draw border background
  doc.image(path.join(__dirname, '../assets/images/page-border.png'), x, y, {
    width: width,
    height: height
  });

  // Set up font and colors - using ForeverBook teal
  doc.font('assets/fonts/Pangolin-Regular.ttf').fillColor('#00A79D');

  // Main greeting text
  const greeting = `${childName},\na special story made just for you—welcome\nto your ForeverBook!`;
  doc.fontSize(22).text(greeting, x, y + 120, {
    width: width,
    align: 'center',
    lineGap: 4
  });

  // ForeverBook teal logo
  const logoPath = path.join(__dirname, '../assets/images/fb_logo_teal.svg');
  const logoWidth = 120;
  const logoHeight = 60;
  doc.svg(fs.readFileSync(logoPath, 'utf8'), x + (width - logoWidth) / 2, y + 220, {
    width: logoWidth,
    height: logoHeight
  });

  // Credits text - using ForeverBook teal
  doc.fontSize(13).fillColor('#00A79D').text(
    'Created for you by the StoryMakers &\nDreamDrawers at ForeverBook Studios',
    x, y + 320, {
    width: width,
    align: 'center',
    lineGap: 2
  }
  );

  // Gift giver text - using ForeverBook teal
  doc.fontSize(13).fillColor('#00A79D').text(
    'A heartfelt gift from Uncle Theo',
    x, y + 360, {
    width: width,
    align: 'center',
    lineGap: 2
  }
  );
}

module.exports = { createPDF }; 